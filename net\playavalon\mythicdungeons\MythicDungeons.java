/*     */ package net.playavalon.mythicdungeons;
/*     */ import com.alessiodp.parties.api.interfaces.PartiesAPI;
/*     */ import com.herocraftonline.heroes.Heroes;
/*     */ import com.onarandombox.multiverseinventories.MultiverseInventories;
/*     */ import io.lumine.mythic.bukkit.MythicBukkit;
/*     */ import java.io.File;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import java.util.UUID;
/*     */ import net.citizensnpcs.Citizens;
/*     */ import net.kyori.adventure.text.minimessage.MiniMessage;
/*     */ import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
/*     */ import net.milkbowl.vault.economy.Economy;
/*     */ import net.playavalon.mythicdungeons.api.MythicDungeonsService;
/*     */ import net.playavalon.mythicdungeons.api.annotations.DeclaredCondition;
/*     */ import net.playavalon.mythicdungeons.api.annotations.DeclaredTrigger;
/*     */ import net.playavalon.mythicdungeons.api.config.AvalonSerializable;
/*     */ import net.playavalon.mythicdungeons.api.generation.layout.LayoutBranching;
/*     */ import net.playavalon.mythicdungeons.api.generation.layout.LayoutMinecrafty;
/*     */ import net.playavalon.mythicdungeons.api.parents.FunctionTargetType;
/*     */ import net.playavalon.mythicdungeons.api.parents.dungeons.AbstractDungeon;
/*     */ import net.playavalon.mythicdungeons.api.parents.instances.AbstractInstance;
/*     */ import net.playavalon.mythicdungeons.api.party.IDungeonParty;
/*     */ import net.playavalon.mythicdungeons.api.queue.QueueData;
/*     */ import net.playavalon.mythicdungeons.avngui.AvnAPI;
/*     */ import net.playavalon.mythicdungeons.bstats.bukkit.Metrics;
/*     */ import net.playavalon.mythicdungeons.compatibility.citizens.MythicNPCRegistry;
/*     */ import net.playavalon.mythicdungeons.dungeons.conditions.ConditionMythic;
/*     */ import net.playavalon.mythicdungeons.dungeons.dungeontypes.DungeonClassic;
/*     */ import net.playavalon.mythicdungeons.dungeons.dungeontypes.DungeonProcedural;
/*     */ import net.playavalon.mythicdungeons.dungeons.functions.FunctionMythicSignal;
/*     */ import net.playavalon.mythicdungeons.dungeons.functions.FunctionMythicSkill;
/*     */ import net.playavalon.mythicdungeons.dungeons.functions.rewards.FunctionRandomReward;
/*     */ import net.playavalon.mythicdungeons.dungeons.rewards.LootCooldown;
/*     */ import net.playavalon.mythicdungeons.dungeons.rewards.LootTableItem;
/*     */ import net.playavalon.mythicdungeons.dungeons.triggers.TriggerMythicMobDeath;
/*     */ import net.playavalon.mythicdungeons.dungeons.variables.VariableEditMode;
/*     */ import net.playavalon.mythicdungeons.gui.GUIHandler;
/*     */ import net.playavalon.mythicdungeons.gui.HotbarMenuHandler;
/*     */ import net.playavalon.mythicdungeons.gui.PlayGUIHandler;
/*     */ import net.playavalon.mythicdungeons.gui.RecruitGUIHandler;
/*     */ import net.playavalon.mythicdungeons.listeners.DynamicListener;
/*     */ import net.playavalon.mythicdungeons.listeners.MythicListener;
/*     */ import net.playavalon.mythicdungeons.listeners.PAPIPlaceholders;
/*     */ import net.playavalon.mythicdungeons.listeners.dungeonlisteners.EditListener;
/*     */ import net.playavalon.mythicdungeons.listeners.dungeonlisteners.InstanceListener;
/*     */ import net.playavalon.mythicdungeons.listeners.partylisteners.HeroesListener;
/*     */ import net.playavalon.mythicdungeons.listeners.partylisteners.PartiesListener;
/*     */ import net.playavalon.mythicdungeons.managers.CommandManager;
/*     */ import net.playavalon.mythicdungeons.managers.ConditionManager;
/*     */ import net.playavalon.mythicdungeons.managers.DungeonManager;
/*     */ import net.playavalon.mythicdungeons.managers.DungeonTypeManager;
/*     */ import net.playavalon.mythicdungeons.managers.FunctionManager;
/*     */ import net.playavalon.mythicdungeons.managers.HotbarMenuManager;
/*     */ import net.playavalon.mythicdungeons.managers.LayoutManager;
/*     */ import net.playavalon.mythicdungeons.managers.LootTableManager;
/*     */ import net.playavalon.mythicdungeons.managers.MovingBlockManager;
/*     */ import net.playavalon.mythicdungeons.managers.PartyManager;
/*     */ import net.playavalon.mythicdungeons.managers.PartyProviderManager;
/*     */ import net.playavalon.mythicdungeons.managers.PartyRecruitmentManager;
/*     */ import net.playavalon.mythicdungeons.managers.PlayerManager;
/*     */ import net.playavalon.mythicdungeons.managers.QueueManager;
/*     */ import net.playavalon.mythicdungeons.managers.TriggerManager;
/*     */ import net.playavalon.mythicdungeons.player.MythicPlayer;
/*     */ import net.playavalon.mythicdungeons.player.party.PartyWrapper;
/*     */ import net.playavalon.mythicdungeons.player.party.partysystem.MythicParty;
/*     */ import net.playavalon.mythicdungeons.utility.ServerVersion;
/*     */ import net.playavalon.mythicdungeons.utility.helpers.LangUtils;
/*     */ import net.playavalon.mythicdungeons.utility.helpers.ReflectionUtils;
/*     */ import net.playavalon.mythicdungeons.utility.helpers.Util;
/*     */ import org.betonquest.betonquest.BetonQuest;
/*     */ import org.bukkit.Bukkit;
/*     */ import org.bukkit.GameRule;
/*     */ import org.bukkit.Location;
/*     */ import org.bukkit.Material;
/*     */ import org.bukkit.command.CommandSender;
/*     */ import org.bukkit.configuration.file.FileConfiguration;
/*     */ import org.bukkit.configuration.serialization.ConfigurationSerialization;
/*     */ import org.bukkit.entity.Player;
/*     */ import org.bukkit.event.Event;
/*     */ import org.bukkit.event.EventException;
/*     */ import org.bukkit.event.EventHandler;
/*     */ import org.bukkit.event.Listener;
/*     */ import org.bukkit.event.player.PlayerTeleportEvent;
/*     */ import org.bukkit.generator.ChunkGenerator;
/*     */ import org.bukkit.plugin.Plugin;
/*     */ import org.bukkit.plugin.RegisteredServiceProvider;
/*     */ import org.bukkit.plugin.ServicePriority;
/*     */ import org.bukkit.plugin.java.JavaPlugin;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ import org.reflections.Reflections;
/*     */ 
/*     */ public final class MythicDungeons extends JavaPlugin implements MythicDungeonsService {
/*     */   private static MythicDungeons plugin;
/*     */   public static String debugPrefix;
/*     */   private FileConfiguration config;
/*     */   private FileConfiguration defaultDungeonConfig;
/*     */   
/*     */   public FileConfiguration getDefaultDungeonConfig() {
/* 104 */     return this.defaultDungeonConfig;
/* 105 */   } private final PlainTextComponentSerializer serializer = PlainTextComponentSerializer.plainText(); public PlainTextComponentSerializer getSerializer() { return this.serializer; }
/* 106 */    private final MiniMessage miniMessage = MiniMessage.miniMessage(); public MiniMessage getMiniMessage() { return this.miniMessage; }
/* 107 */    private final HashMap<String, GameRule<Boolean>> booleanGameRules = new HashMap<>(); public HashMap<String, GameRule<Boolean>> getBooleanGameRules() { return this.booleanGameRules; }
/* 108 */    private final HashMap<String, GameRule<Integer>> intGameRules = new HashMap<>(); private Material functionBuilderMaterial; private Material roomEditorMaterial; private boolean stuckKillsPlayer; private boolean inheritedVelocityEnabled; private boolean paperSupport; private boolean supportsTeleportFlags; public HashMap<String, GameRule<Integer>> getIntGameRules() { return this.intGameRules; }
/*     */   
/* 110 */   public Material getFunctionBuilderMaterial() { return this.functionBuilderMaterial; }
/* 111 */   public Material getRoomEditorMaterial() { return this.roomEditorMaterial; }
/* 112 */   public boolean isStuckKillsPlayer() { return this.stuckKillsPlayer; }
/* 113 */   public boolean isInheritedVelocityEnabled() { return this.inheritedVelocityEnabled; }
/* 114 */   public boolean isPaperSupport() { return this.paperSupport; }
/* 115 */   public boolean isSupportsTeleportFlags() { return this.supportsTeleportFlags; }
/* 116 */   private boolean supportsTeleportAsync = true; private boolean supportsFAWE; private AvnAPI avnAPI; private MythicBukkit mythicApi; private Citizens citizensApi; private MythicNPCRegistry mythicNPCRegistry; private Economy economy; private MultiverseInventories multiverseInv; private boolean betonEnabled; private boolean faweEnabled; private boolean partiesEnabled; private String partyPluginName; private boolean thirdPartyProvider; private Heroes heroesApi; private PartiesAPI partiesAPI; private File dungeonFiles; public boolean isSupportsTeleportAsync() { return this.supportsTeleportAsync; } private File backupFolder; private File playerDataFolder; private PlayerManager playerManager; private PartyManager partyManager; private PartyProviderManager providerManager; private DungeonManager dungeons; private List<AbstractInstance> activeInstances; private FunctionManager functionManager; private TriggerManager triggerManager; private ConditionManager conditionManager; private QueueManager queueManager; private LootTableManager lootTableManager; private MovingBlockManager movingBlockManager; private PartyRecruitmentManager listingManager; private HotbarMenuManager hotbarMenus; private CommandManager commandManager; public boolean isSupportsFAWE() {
/* 117 */     return this.supportsFAWE;
/*     */   }
/*     */   
/* 120 */   public AvnAPI getAvnAPI() { return this.avnAPI; }
/* 121 */   public MythicBukkit getMythicApi() { return this.mythicApi; }
/* 122 */   public Citizens getCitizensApi() { return this.citizensApi; }
/* 123 */   public MythicNPCRegistry getMythicNPCRegistry() { return this.mythicNPCRegistry; }
/* 124 */   public Economy getEconomy() { return this.economy; } public MultiverseInventories getMultiverseInv() {
/* 125 */     return this.multiverseInv;
/*     */   }
/* 127 */   public boolean isBetonEnabled() { return this.betonEnabled; } public void setBetonEnabled(boolean betonEnabled) { this.betonEnabled = betonEnabled; }
/* 128 */   public boolean isFaweEnabled() { return this.faweEnabled; } public void setFaweEnabled(boolean faweEnabled) { this.faweEnabled = faweEnabled; }
/*     */ 
/*     */   
/* 131 */   public boolean isPartiesEnabled() { return this.partiesEnabled; }
/* 132 */   public String getPartyPluginName() { return this.partyPluginName; }
/* 133 */   public boolean isThirdPartyProvider() { return this.thirdPartyProvider; }
/* 134 */   public Heroes getHeroesApi() { return this.heroesApi; } public PartiesAPI getPartiesAPI() {
/* 135 */     return this.partiesAPI;
/*     */   }
/*     */   
/* 138 */   public File getDungeonFiles() { return this.dungeonFiles; }
/* 139 */   public File getBackupFolder() { return this.backupFolder; } public File getPlayerDataFolder() {
/* 140 */     return this.playerDataFolder;
/*     */   }
/*     */   
/* 143 */   public PlayerManager getPlayerManager() { return this.playerManager; }
/* 144 */   public PartyManager getPartyManager() { return this.partyManager; }
/* 145 */   public PartyProviderManager getProviderManager() { return this.providerManager; }
/* 146 */   public DungeonManager getDungeons() { return this.dungeons; }
/* 147 */   public List<AbstractInstance> getActiveInstances() { return this.activeInstances; }
/* 148 */   public FunctionManager getFunctionManager() { return this.functionManager; }
/* 149 */   public TriggerManager getTriggerManager() { return this.triggerManager; }
/* 150 */   public ConditionManager getConditionManager() { return this.conditionManager; }
/* 151 */   public QueueManager getQueueManager() { return this.queueManager; }
/* 152 */   public LootTableManager getLootTableManager() { return this.lootTableManager; }
/* 153 */   public MovingBlockManager getMovingBlockManager() { return this.movingBlockManager; }
/* 154 */   public PartyRecruitmentManager getListingManager() { return this.listingManager; }
/* 155 */   public HotbarMenuManager getHotbarMenus() { return this.hotbarMenus; } public CommandManager getCommandManager() {
/* 156 */     return this.commandManager;
/*     */   }
/*     */   
/* 159 */   private DynamicListener elementEventHandler = new DynamicListener(); public DynamicListener getElementEventHandler() { return this.elementEventHandler; }
/* 160 */    private DynamicListener instanceEventHandler = new DynamicListener(); public DynamicListener getInstanceEventHandler() { return this.instanceEventHandler; }
/*     */ 
/*     */ 
/*     */   
/*     */   public void onEnable() {
/* 165 */     plugin = this;
/* 166 */     saveDefaultConfig();
/* 167 */     this.config = getConfig();
/* 168 */     this.defaultDungeonConfig = (FileConfiguration)new YamlConfiguration();
/* 169 */     debugPrefix = Util.modernizeColors("<#9753f5>[Dungeons] ");
/*     */     
/* 171 */     if (!ServerVersion.get().isAfterOrEqual(ServerVersion.v1_19_3)) {
/* 172 */       inst().getComponentLogger().info(Util.modernizeColorsComponent(debugPrefix + "ALERT :: UNSUPPORTED MINECRAFT VERSION. Mythic Dungeons is designed for Minecraft 1.19.3+!!"));
/* 173 */       inst().getComponentLogger().info(Util.modernizeColorsComponent(debugPrefix + "-- Function labels will not appear when editing dungeons and there may be an increase in memory leaks!"));
/*     */     } 
/*     */ 
/*     */     
/* 177 */     loadGameRules();
/*     */     
/*     */     try {
/* 180 */       Class.forName("com.destroystokyo.paper.ParticleBuilder");
/* 181 */       this.paperSupport = true;
/*     */       try {
/* 183 */         Class.forName("io.papermc.paper.entity.TeleportFlag");
/* 184 */         this.supportsTeleportFlags = true;
/*     */         
/* 186 */         try { Class<?> clazz = Class.forName("org.bukkit.entity.Entity");
/* 187 */           clazz.getMethod("teleportAsync", new Class[] { Location.class, PlayerTeleportEvent.TeleportCause.class, TeleportFlag[].class }); }
/* 188 */         catch (ClassNotFoundException|NoSuchMethodException ignored) { this.supportsTeleportAsync = false; } 
/* 189 */       } catch (ClassNotFoundException classNotFoundException) {}
/* 190 */     } catch (ClassNotFoundException e) {
/* 191 */       inst().getComponentLogger().info(Util.modernizeColorsComponent(debugPrefix + "&eWARNING :: You're not using Paper Spigot! It is highly advised to use Paper (or a fork of Paper) for better dungeon performance!"));
/*     */     } 
/*     */     
/* 194 */     ReflectionUtils.prepMemoryLeakKiller();
/*     */     
/* 196 */     this.dungeonFiles = new File(plugin.getDataFolder(), "maps");
/* 197 */     this.backupFolder = new File(plugin.getDataFolder(), "backups");
/* 198 */     if (!this.backupFolder.exists() || !this.backupFolder.isDirectory()) this.backupFolder.mkdir(); 
/* 199 */     this.playerDataFolder = new File(plugin.getDataFolder(), "globalplayerdata");
/* 200 */     if (!this.playerDataFolder.exists()) this.playerDataFolder.mkdir();
/*     */     
/* 202 */     LangUtils.init();
/* 203 */     LangUtils.saveMissingValues();
/*     */     
/*     */     try {
/* 206 */       this.functionBuilderMaterial = Material.getMaterial(this.config.getString("General.FunctionBuilderItem", "FEATHER"));
/* 207 */     } catch (IllegalArgumentException e) {
/* 208 */       inst().getComponentLogger().info(Util.modernizeColorsComponent("&cWARNING :: FunctionBuilderItem in config.yml must be a valid material! Using FEATHER by default..."));
/* 209 */       this.functionBuilderMaterial = Material.FEATHER;
/*     */     } 
/*     */     try {
/* 212 */       this.roomEditorMaterial = Material.getMaterial(this.config.getString("General.RoomEditorItem", "GOLDEN_AXE"));
/* 213 */     } catch (IllegalArgumentException e) {
/* 214 */       inst().getComponentLogger().info(Util.modernizeColorsComponent("&cWARNING :: RoomEditorItem in config.yml must be a valid material! Using GOLDEN_AXE by default..."));
/* 215 */       this.roomEditorMaterial = Material.GOLDEN_AXE;
/*     */     } 
/* 217 */     this.stuckKillsPlayer = this.config.getBoolean("General.StuckKillsPlayer", false);
/* 218 */     this.inheritedVelocityEnabled = this.config.getBoolean("Experimental.MovingBlocksMoveEntities", false);
/*     */ 
/*     */     
/* 221 */     this.avnAPI = new AvnAPI(this);
/* 222 */     AvnGUI.debug = false;
/* 223 */     Metrics metrics = new Metrics(this, 16578);
/*     */ 
/*     */     
/* 226 */     ConfigurationSerialization.registerClass(PlayerLootData.class);
/* 227 */     ConfigurationSerialization.registerClass(LootCooldown.class);
/* 228 */     ConfigurationSerialization.registerClass(FunctionTargetType.class);
/* 229 */     ConfigurationSerialization.registerClass(LootTable.class);
/* 230 */     ConfigurationSerialization.registerClass(LootTableItem.class);
/* 231 */     ConfigurationSerialization.registerClass(VariableEditMode.class);
/*     */ 
/*     */     
/* 234 */     this.partyPluginName = this.config.getString("General.PartyPlugin", "Default");
/*     */ 
/*     */ 
/*     */     
/* 238 */     this.playerManager = new PlayerManager();
/* 239 */     this.partyManager = new PartyManager();
/* 240 */     this.providerManager = new PartyProviderManager();
/* 241 */     this.activeInstances = new ArrayList<>();
/* 242 */     this.functionManager = new FunctionManager();
/* 243 */     this.triggerManager = new TriggerManager();
/* 244 */     this.conditionManager = new ConditionManager();
/* 245 */     this.queueManager = new QueueManager();
/* 246 */     this.lootTableManager = new LootTableManager();
/* 247 */     this.commandManager = new CommandManager(this);
/* 248 */     this.movingBlockManager = new MovingBlockManager();
/*     */ 
/*     */     
/* 251 */     if (this.paperSupport) {
/* 252 */       getComponentLogger().info("Using PaperListener...");
/* 253 */       Bukkit.getPluginManager().registerEvents((Listener)new PaperListener(), (Plugin)this);
/*     */     } else {
/* 255 */       Bukkit.getPluginManager().registerEvents((Listener)new AvalonListener(), (Plugin)this);
/* 256 */     }  Bukkit.getPluginManager().registerEvents((Listener)new MythicPartyListener(), (Plugin)this);
/*     */ 
/*     */ 
/*     */     
/* 260 */     if (this.partyPluginName.equalsIgnoreCase("Default") || this.partyPluginName.equalsIgnoreCase("DungeonParties")) {
/* 261 */       this.partiesEnabled = true;
/*     */ 
/*     */       
/* 264 */       this.listingManager = new PartyRecruitmentManager();
/* 265 */       System.out.println(Util.modernizeColors("Using default parties! Enabled party support."));
/* 266 */       RecruitGUIHandler.initPartyBrowser();
/* 267 */     } else if (Bukkit.getPluginManager().getPlugin(this.partyPluginName) != null) {
/* 268 */       this.partiesEnabled = true;
/*     */       
/* 270 */       switch (this.partyPluginName.toLowerCase()) {
/*     */         case "heroes":
/* 272 */           this.thirdPartyProvider = true;
/* 273 */           this.listingManager = new PartyRecruitmentManager();
/* 274 */           this.heroesApi = Heroes.getInstance();
/* 275 */           Bukkit.getPluginManager().registerEvents((Listener)new HeroesListener(), (Plugin)plugin);
/*     */           break;
/*     */         case "parties":
/* 278 */           this.thirdPartyProvider = true;
/* 279 */           this.listingManager = new PartyRecruitmentManager();
/* 280 */           this.partiesAPI = Parties.getApi();
/* 281 */           Bukkit.getPluginManager().registerEvents((Listener)new PartiesListener(), (Plugin)plugin);
/*     */           break;
/*     */         default:
/* 284 */           if (Bukkit.getPluginManager().getPlugin(this.partyPluginName) == null) {
/* 285 */             this.partiesEnabled = false;
/* 286 */             inst().getComponentLogger().info(Util.modernizeColorsComponent("&cERROR :: '" + this.partyPluginName + "' was not found! Party support not enabled."));
/*     */           } 
/*     */           break;
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 294 */       if (this.partiesEnabled) {
/* 295 */         inst().getComponentLogger().info(Util.modernizeColorsComponent("&d" + this.partyPluginName + " plugin found! Enabled party support."));
/* 296 */         RecruitGUIHandler.initPartyBrowser();
/* 297 */         PartyWrapper.setPartyPlugin(this.partyPluginName);
/*     */       } 
/*     */     } else {
/*     */       
/* 301 */       inst().getComponentLogger().info(Util.modernizeColorsComponent("&cERROR :: Party plugin is set to '" + this.partyPluginName + "', but no such plugin was found!"));
/*     */     } 
/*     */     
/* 304 */     this.hotbarMenus = new HotbarMenuManager();
/* 305 */     Bukkit.getPluginManager().registerEvents((Listener)this.hotbarMenus, (Plugin)this);
/*     */ 
/*     */ 
/*     */     
/* 309 */     if (Bukkit.getPluginManager().getPlugin("MythicMobs") != null) {
/* 310 */       inst().getComponentLogger().info(Util.modernizeColorsComponent("&dMythicMobs plugin found! Enabled MythicMobs support."));
/* 311 */       this.mythicApi = MythicBukkit.inst();
/* 312 */       Bukkit.getPluginManager().registerEvents((Listener)new MythicListener(), (Plugin)this);
/*     */     } 
/* 314 */     if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
/* 315 */       (new PAPIPlaceholders(this)).register();
/*     */     }
/*     */     
/* 318 */     if (Bukkit.getPluginManager().getPlugin("Heroes") != null) {
/* 319 */       this.heroesApi = Heroes.getInstance();
/*     */     }
/*     */     
/* 322 */     if (Bukkit.getPluginManager().getPlugin("Citizens") != null) {
/* 323 */       this.citizensApi = (Citizens)Bukkit.getPluginManager().getPlugin("Citizens");
/* 324 */       this.mythicNPCRegistry = new MythicNPCRegistry();
/*     */     } 
/*     */     
/* 327 */     Plugin beton = Bukkit.getPluginManager().getPlugin("BetonQuest");
/* 328 */     if (beton != null) {
/* 329 */       if (!beton.getDescription().getVersion().startsWith("2")) {
/* 330 */         inst().getComponentLogger().info(Util.modernizeColorsComponent("&eWARNING :: Incompatible BetonQuest version! You must use version 2.0 or higher!"));
/* 331 */         inst().getComponentLogger().info(Util.modernizeColorsComponent("&cBetonQuest compatibility has not been enabled."));
/*     */       }
/* 333 */       else if (BetonQuest.getInstance() != null) {
/* 334 */         inst().getComponentLogger().info(Util.modernizeColorsComponent("&dBetonQuest plugin found! Enabled BetonQuest support."));
/* 335 */         this.betonEnabled = true;
/* 336 */         BetonQuest.getInstance().registerObjectives("finishdungeon", CompleteDungeonObjective.class);
/* 337 */         BetonQuest.getInstance().registerEvents("playdungeon", EnterDungeonEvent.class);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 342 */     setupEconomy();
/*     */     
/* 344 */     AvalonSerializable.register((Plugin)this);
/*     */ 
/*     */     
/* 347 */     registerInstanceListener(PlayListener.class);
/* 348 */     registerInstanceListener(EditListener.class);
/*     */ 
/*     */     
/* 351 */     registerDungeonType(DungeonClassic.class, "classic", new String[] { "default" });
/* 352 */     registerDungeonType(DungeonContinuous.class, "continuous", new String[] { "ongoing" });
/* 353 */     registerDungeonType(DungeonProcedural.class, "procedural", new String[] { "generated", "room" });
/*     */ 
/*     */     
/* 356 */     registerLayout(LayoutMinecrafty.class, "minecrafty", new String[] { "minecraft", "vanilla", "random" });
/* 357 */     registerLayout(LayoutBranching.class, "branching", new String[] { "branch", "linear" });
/*     */ 
/*     */     
/* 360 */     registerFunctions("net.playavalon.mythicdungeons.dungeons.functions");
/* 361 */     registerFunction(FunctionReward.class);
/* 362 */     registerFunction(FunctionRandomReward.class);
/*     */ 
/*     */     
/* 365 */     registerTriggers("net.playavalon.mythicdungeons.dungeons.triggers");
/*     */ 
/*     */     
/* 368 */     registerConditions("net.playavalon.mythicdungeons.dungeons.conditions");
/*     */ 
/*     */     
/* 371 */     if (this.mythicApi != null) {
/* 372 */       registerFunction(FunctionMythicLootTableRewards.class);
/* 373 */       registerFunction(FunctionMythicSkill.class);
/* 374 */       registerFunction(FunctionMythicSignal.class);
/* 375 */       registerTrigger(TriggerMythicMobDeath.class);
/* 376 */       registerCondition(ConditionMythic.class);
/*     */     } else {
/* 378 */       registerFunction(FunctionLootTableRewards.class);
/* 379 */       registerTrigger(TriggerMobDeath.class);
/*     */     } 
/*     */     
/* 382 */     if (this.citizensApi != null) {
/* 383 */       registerFunction(FunctionSpawnNPC.class);
/*     */     }
/*     */     
/* 386 */     inst().getComponentLogger().info(Util.modernizeColorsComponent("&d* Loaded &6" + this.functionManager.getAll().size() + " &dfunctions."));
/* 387 */     inst().getComponentLogger().info(Util.modernizeColorsComponent("&d* Loaded &6" + this.triggerManager.getAll().size() + " &dtriggers."));
/* 388 */     inst().getComponentLogger().info(Util.modernizeColorsComponent("&d* Loaded &6" + this.conditionManager.getAll().size() + " &dconditions."));
/*     */     
/* 390 */     GUIHandler.initFunctionMenu();
/* 391 */     GUIHandler.initTriggerMenu();
/* 392 */     GUIHandler.initConditionsMenus();
/* 393 */     GUIHandler.initGateTriggerMenus();
/* 394 */     GUIHandler.initItemSelectTriggerMenu();
/* 395 */     GUIHandler.initItemSelectFunctionMenu();
/* 396 */     PlayGUIHandler.initRewardMenu();
/* 397 */     PlayGUIHandler.initRevivalMenu();
/* 398 */     GUIHandler.initMultiFunctionMenus();
/* 399 */     RoomGUIHandler.initConnectorWhitelist();
/*     */
/*     */     // Initialize Enhanced GUI System
/*     */     try {
/*     */       net.playavalon.mythicdungeons.gui.enhanced.EnhancedGUIManager.initializeEnhancedGUIs();
/*     */       inst().getComponentLogger().info(Util.modernizeColorsComponent("&aEnhanced GUI system initialized successfully!"));
/*     */     } catch (Exception e) {
/*     */       inst().getComponentLogger().error("Failed to initialize enhanced GUI system: " + e.getMessage());
/*     */       e.printStackTrace();
/*     */     }
/*     */     
/* 401 */     HotbarMenuHandler.initEditMenu();
/* 402 */     HotbarMenuHandler.initRoomEditMenu();
/* 403 */     HotbarMenuHandler.initRoomRulesMenu();
/*     */     
/* 405 */     inst().getComponentLogger().info(Util.modernizeColorsComponent("&dGUI menus initialized!"));
/*     */ 
/*     */     
/* 408 */     this.dungeons = new DungeonManager();
/*     */ 
/*     */ 
/*     */     
/* 412 */     if (!Bukkit.getOnlinePlayers().isEmpty()) {
/* 413 */       for (Player player : Bukkit.getOnlinePlayers()) {
/* 414 */         plugin.getPlayerManager().put(player);
/*     */       }
/*     */     }
/*     */     
/* 418 */     inst().getComponentLogger().info(Util.modernizeColorsComponent("&dMythic Dungeons v" + getVersion() + " &ainitialized! Happy dungeon-ing!"));
/*     */     
/* 420 */     getServer().getServicesManager().register(MythicDungeonsService.class, this, (Plugin)this, ServicePriority.Normal);
/*     */     
/* 422 */     Bukkit.getScheduler().runTaskLater((Plugin)this, () -> this.multiverseInv = (MultiverseInventories)Bukkit.getServer().getPluginManager().getPlugin("Multiverse-Inventories"), 1L);
/*     */   }
/*     */   
/*     */   private void loadGameRules() {
/* 426 */     Arrays.<GameRule>stream(GameRule.values()).toList().forEach(rule -> {
/*     */           if (rule.getType().equals(Boolean.class)) {
/*     */             this.booleanGameRules.put(rule.getName(), rule);
/*     */           } else if (rule.getType().equals(Integer.class)) {
/*     */             this.intGameRules.put(rule.getName(), rule);
/*     */           } 
/*     */         });
/*     */   }
/*     */   
/*     */   private boolean setupEconomy() {
/* 436 */     if (getServer().getPluginManager().getPlugin("Vault") == null) {
/* 437 */       return false;
/*     */     }
/*     */     
/* 440 */     RegisteredServiceProvider<Economy> rsp = getServer().getServicesManager().getRegistration(Economy.class);
/* 441 */     if (rsp == null) {
/* 442 */       return false;
/*     */     }
/*     */     
/* 445 */     this.economy = (Economy)rsp.getProvider();
/* 446 */     return (this.economy != null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void onDisable() {
/* 452 */     inst().getComponentLogger().info(Util.modernizeColorsComponent("&dCleaning up dungeons..."));
/*     */     
/* 454 */     for (Player player : Bukkit.getOnlinePlayers()) {
/* 455 */       MythicPlayer aPlayer = getMythicPlayer(player);
/*     */       
/* 457 */       aPlayer.restoreHotbar();
/* 458 */       AbstractInstance instance = aPlayer.getInstance();
/* 459 */       if (instance != null) aPlayer.getInstance().removePlayer(aPlayer);
/*     */     
/*     */     } 
/*     */     
/* 463 */     if (this.dungeons == null)
/*     */       return; 
/* 465 */     for (AbstractDungeon dungeon : this.dungeons.getAll()) {
/* 466 */       List<AbstractInstance> instances = new ArrayList<>(dungeon.getInstances());
/* 467 */       for (AbstractInstance instance : instances) {
/* 468 */         instance.dispose();
/*     */       }
/*     */     } 
/*     */     
/* 472 */     this.movingBlockManager.disable();
/*     */   }
/*     */ 
/*     */   
/*     */   public void reload() {
/* 477 */     reloadAllDungeons();
/* 478 */     reloadConfigs();
/*     */   }
/*     */   public void reloadAllDungeons() {
/* 481 */     for (Player player : Bukkit.getOnlinePlayers()) {
/* 482 */       MythicPlayer aPlayer = getMythicPlayer(player);
/*     */       
/* 484 */       aPlayer.restoreHotbar();
/* 485 */       AbstractInstance instance = aPlayer.getInstance();
/* 486 */       if (instance != null) {
/* 487 */         instance.removePlayer(aPlayer);
/* 488 */         aPlayer.getPlayer().sendMessage(Util.modernizeColorsComponent(debugPrefix + "&cThe dungeon was reloaded by an admin!"));
/*     */       } 
/*     */     } 
/*     */     
/* 492 */     (new ProcessTimer()).run("Reloading Dungeons", () -> this.dungeons = new DungeonManager());
/*     */   }
/*     */   
/*     */   public void reloadDungeon(AbstractDungeon dungeon) {
/* 496 */     List<AbstractInstance> instances = new ArrayList<>(dungeon.getInstances());
/* 497 */     for (AbstractInstance instance : instances) {
/* 498 */       List<MythicPlayer> players = new ArrayList<>(instance.getPlayers());
/* 499 */       for (MythicPlayer aPlayer : players) {
/* 500 */         aPlayer.restoreHotbar();
/* 501 */         instance.removePlayer(aPlayer);
/* 502 */         aPlayer.getPlayer().sendMessage(Util.modernizeColorsComponent(debugPrefix + "&cThe dungeon was reloaded by an admin!"));
/*     */       } 
/*     */     } 
/*     */     
/* 506 */     this.dungeons.remove(dungeon);
/* 507 */     this.dungeons.loadDungeon(dungeon.getFolder());
/*     */   }
/*     */   public void reloadConfigs() {
/* 510 */     reloadConfig();
/* 511 */     this.config = getConfig();
/*     */     
/*     */     try {
/* 514 */       this.functionBuilderMaterial = Material.getMaterial(this.config.getString("General.FunctionBuilderItem", "FEATHER"));
/* 515 */     } catch (IllegalArgumentException e) {
/* 516 */       inst().getComponentLogger().info(Util.modernizeColorsComponent("&cWARNING: FunctionBuilderItem in config.yml must be a valid material! Using FEATHER by default..."));
/* 517 */       this.functionBuilderMaterial = Material.FEATHER;
/*     */     } 
/* 519 */     this.stuckKillsPlayer = this.config.getBoolean("General.StuckKillsPlayer", false);
/*     */     
/* 521 */     LangUtils.init();
/* 522 */     this.lootTableManager = new LootTableManager();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getVersion() {
/* 530 */     return getDescription().getVersion().split("-")[0];
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBuildNumber() {
/* 538 */     String[] split = getDescription().getVersion().split("-");
/* 539 */     if (split.length == 2)
/* 540 */       return split[1]; 
/* 541 */     if (split.length == 3) {
/* 542 */       return split[2];
/*     */     }
/* 544 */     return "????";
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public ChunkGenerator getDefaultWorldGenerator(@NotNull String worldName, @Nullable String id) {
/* 550 */     if (id == null) return super.getDefaultWorldGenerator(worldName, id);
/*     */     
/* 552 */     if (id.toLowerCase().startsWith("block") && id.contains(".")) {
/* 553 */       String[] split = id.split("\\.", 2);
/*     */       
/* 555 */       Material material = Material.STONE;
/*     */       try {
/* 557 */         material = Material.valueOf(split[1]);
/* 558 */       } catch (IllegalArgumentException illegalArgumentException) {}
/*     */       
/* 560 */       return (ChunkGenerator)new FullBlockGenerator(material);
/*     */     } 
/*     */     
/* 563 */     if (id.equalsIgnoreCase("void")) {
/* 564 */       return (ChunkGenerator)new VoidGenerator();
/*     */     }
/* 566 */     return super.getDefaultWorldGenerator(worldName, id);
/*     */   }
/*     */ 
/*     */   
/*     */   public MythicPlayer getMythicPlayer(Player player) {
/* 571 */     if (getMythicPlayer(player.getUniqueId()) == null) inst().getPlayerManager().put(player); 
/* 572 */     return getMythicPlayer(player.getUniqueId());
/*     */   } public MythicPlayer getMythicPlayer(UUID uuid) {
/* 574 */     return this.playerManager.get(uuid);
/*     */   }
/*     */   public boolean sendToDungeon(Player player, String dungeonName) {
/* 577 */     return sendToDungeon(player, dungeonName, "DEFAULT");
/*     */   }
/*     */   public boolean sendToDungeon(Player player, String dungeonName, String difficulty) {
/* 580 */     MythicPlayer aPlayer = getMythicPlayer(player);
/* 581 */     AbstractDungeon targetDungeon = this.dungeons.get(dungeonName);
/*     */     
/* 583 */     if (this.partiesEnabled) {
/* 584 */       PartyWrapper partyWrapper; IDungeonParty party = aPlayer.getDungeonParty();
/* 585 */       if (this.thirdPartyProvider) {
/*     */         
/* 587 */         if (party == null) partyWrapper = new PartyWrapper(aPlayer); 
/* 588 */         if (partyWrapper.getPlayers().size() == 1) partyWrapper = null;
/*     */       
/*     */       } 
/*     */       
/* 592 */       if (partyWrapper != null)
/*     */       {
/* 594 */         if (this.config.getBoolean("General.LeaderOnlyQueue", true) && player != aPlayer.getDungeonParty().getLeader()) {
/* 595 */           LangUtils.sendMessage((CommandSender)player, "commands.play.party-lead-only");
/* 596 */           return false;
/*     */         } 
/*     */ 
/*     */         
/* 600 */         boolean success = true;
/* 601 */         for (Player partyPlayer : partyWrapper.getPlayers()) {
/* 602 */           MythicPlayer mPlayer = getMythicPlayer(partyPlayer);
/* 603 */           if (mPlayer.getInstance() != null) {
/* 604 */             LangUtils.sendMessage((CommandSender)player, "commands.play.player-in-dungeon", new String[] { partyPlayer.getName() });
/* 605 */             success = false;
/*     */           } 
/*     */         } 
/*     */         
/* 609 */         if (!success) return false; 
/* 610 */         if (!targetDungeon.partyMeetsRequirements((IDungeonParty)partyWrapper)) return false;
/*     */         
/* 612 */         partyWrapper.setAwaitingDungeon(true);
/*     */       }
/*     */       else
/*     */       {
/* 616 */         if (!targetDungeon.partyMeetsRequirements(player)) return false; 
/* 617 */         aPlayer.setAwaitingDungeon(true);
/*     */       }
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 623 */       if (!targetDungeon.partyMeetsRequirements(player)) return false; 
/* 624 */       aPlayer.setAwaitingDungeon(true);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 629 */     QueueData queue = new QueueData(aPlayer, targetDungeon, difficulty);
/* 630 */     this.queueManager.enqueue(queue);
/*     */ 
/*     */     
/* 633 */     if (this.activeInstances.size() >= this.config.getInt("General.MaxInstances", 10) || !targetDungeon.hasAvailableInstances()) {
/* 634 */       LangUtils.sendMessage((CommandSender)player, "commands.play.instances-full");
/* 635 */       LangUtils.sendMessage((CommandSender)player, "commands.play.how-to-cancel");
/* 636 */       return true;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 643 */     boolean immediate = (aPlayer.getDungeonParty() == null || aPlayer.getDungeonParty().getPlayers().size() == 1 || !this.config.getBoolean("General.ReadyCheckOnCommand", true));
/*     */     
/* 645 */     queue.enterDungeon(immediate);
/*     */     
/* 647 */     return true;
/*     */   }
/*     */   
/*     */   public File getDungeonsFolder() {
/* 651 */     return this.dungeonFiles;
/*     */   }
/*     */   
/*     */   public DungeonManager getDungeonManager() {
/* 655 */     return this.dungeons;
/*     */   }
/*     */   
/*     */   public <T extends AbstractDungeon> void registerDungeonType(Class<T> type, String name, String... aliases) {
/* 659 */     DungeonTypeManager.register(type, name, aliases);
/*     */   }
/*     */   
/*     */   public <T extends net.playavalon.mythicdungeons.api.generation.layout.Layout> void registerLayout(Class<T> type, String name, String... aliases) {
/* 663 */     LayoutManager.register(type, name, aliases);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public <T extends InstanceListener> void registerInstanceListener(Class<T> type) {
/* 669 */     List<Method> eventMethods = new ArrayList<>();
/* 670 */     ReflectionUtils.getAnnotatedMethods(eventMethods, type, EventHandler.class);
/*     */     
/* 672 */     for (Method method : eventMethods) {
/* 673 */       EventHandler handler = method.<EventHandler>getAnnotation(EventHandler.class);
/* 674 */       Class<?> rawEvent = method.getParameterTypes()[0];
/* 675 */       if (!Event.class.isAssignableFrom(rawEvent))
/* 676 */         continue;  Class<? extends Event> eventClass = rawEvent.asSubclass(Event.class);
/*     */       
/* 678 */       Bukkit.getPluginManager().registerEvent(eventClass, (Listener)inst().getInstanceEventHandler(), handler.priority(), (listener, event) -> {
/*     */             List<AbstractInstance> instances = inst().getActiveInstances();
/*     */             for (AbstractInstance inst : instances) {
/*     */               if (!eventClass.isAssignableFrom(event.getClass())) {
/*     */                 continue;
/*     */               }
/*     */               InstanceListener instListener = inst.getListener();
/*     */               if (instListener == null || !type.isAssignableFrom(instListener.getClass())) {
/*     */                 continue;
/*     */               }
/*     */               try {
/*     */                 method.invoke(instListener, new Object[] { event });
/* 690 */               } catch (Exception e) {
/*     */                 
/*     */                 e.printStackTrace();
/*     */               }
/*     */             
/*     */             } 
/* 696 */           }(Plugin)inst());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public <T extends net.playavalon.mythicdungeons.api.parents.elements.DungeonFunction> void registerFunction(Class<T> function) {
/* 703 */     this.functionManager.register(function);
/*     */     
/* 705 */     ConfigurationSerialization.registerClass(function);
/*     */   }
/*     */   
/*     */   public <T extends net.playavalon.mythicdungeons.api.parents.elements.DungeonTrigger> void registerTrigger(Class<T> trigger) {
/* 709 */     this.triggerManager.register(trigger);
/*     */     
/* 711 */     ConfigurationSerialization.registerClass(trigger);
/*     */   }
/*     */   
/*     */   public <T extends net.playavalon.mythicdungeons.api.parents.elements.TriggerCondition> void registerCondition(Class<T> condition) {
/* 715 */     this.conditionManager.register(condition);
/*     */     
/* 717 */     ConfigurationSerialization.registerClass(condition);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void registerFunctions(String functionsPackage) {
/* 723 */     Set<Class<?>> classes = (new Reflections(functionsPackage, new org.reflections.scanners.Scanner[0])).getTypesAnnotatedWith(DeclaredFunction.class);
/*     */     
/* 725 */     for (Class<?> clazz : classes)
/* 726 */       registerFunction(clazz); 
/*     */   }
/*     */   
/*     */   public void registerTriggers(String triggersPackage) {
/* 730 */     Set<Class<?>> classes = (new Reflections(triggersPackage, new org.reflections.scanners.Scanner[0])).getTypesAnnotatedWith(DeclaredTrigger.class);
/*     */     
/* 732 */     for (Class<?> clazz : classes)
/* 733 */       registerTrigger(clazz); 
/*     */   }
/*     */   
/*     */   public void registerConditions(String conditionsPackage) {
/* 737 */     Set<Class<?>> classes = (new Reflections(conditionsPackage, new org.reflections.scanners.Scanner[0])).getTypesAnnotatedWith(DeclaredCondition.class);
/*     */     
/* 739 */     for (Class<?> clazz : classes) {
/* 740 */       registerCondition(clazz);
/*     */     }
/*     */   }
/*     */   
/*     */   public static MythicDungeons inst() {
/* 745 */     return (MythicDungeons)getPlugin(MythicDungeons.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean checkIfInternalPartySystem() {
/* 753 */     if (this.partiesEnabled && (this.partyPluginName.equals("DungeonParties") || this.partyPluginName.equals("Default"))) return true; 
/* 754 */     getComponentLogger().info("Cannot process API call. Party system is not enabled or the internal party system is not selected.");
/*     */     
/* 756 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isPlayerInDungeon(Player player) {
/* 762 */     if (!getPlayerManager().contains(player)) return false; 
/* 763 */     return (getPlayerManager().get(player.getUniqueId()).getInstance() != null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public AbstractInstance getDungeonInstance(Player player) {
/* 769 */     if (!getPlayerManager().contains(player)) return null; 
/* 770 */     return getPlayerManager().get(player.getUniqueId()).getInstance();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public AbstractInstance getDungeonInstance(String worldName) {
/* 776 */     for (AbstractInstance instance : this.activeInstances) {
/* 777 */       if (instance.getInstanceWorld().getName().equals(worldName)) return instance; 
/*     */     } 
/* 779 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Collection<AbstractDungeon> getAllDungeons() {
/* 785 */     return this.dungeons.getAll();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean initiateDungeonForPlayer(Player player, String dungeonName) {
/*     */     AbstractDungeon dungeon;
/* 792 */     if ((dungeon = getDungeonManager().get(dungeonName)) == null) {
/* 793 */       LangUtils.sendMessage((CommandSender)player, "commands.play.dungeon-not-found", new String[] { dungeonName });
/* 794 */       return false;
/*     */     } 
/*     */     
/* 797 */     MythicPlayer mPlayer = getMythicPlayer(player);
/* 798 */     if (mPlayer.getInstance() != null && 
/* 799 */       mPlayer.getInstance().getDungeon() != dungeon) {
/* 800 */       LangUtils.sendMessage((CommandSender)player, "commands.play.player-in-dungeon", new String[] { player.getName() });
/* 801 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 805 */     if (!Util.hasPermission((CommandSender)player, "dungeons.play")) {
/* 806 */       LangUtils.sendMessage((CommandSender)player, "commands.play.requirements-not-met", new String[] { player.getName() });
/* 807 */       return false;
/*     */     } 
/*     */     
/* 810 */     sendToDungeon(player, dungeonName);
/*     */     
/* 812 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean createParty(Player target) {
/* 823 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 825 */     MythicPlayer mythicPlayer = getMythicPlayer(target);
/*     */     
/* 827 */     if (mythicPlayer.getMythicParty() != null) return false;
/*     */     
/* 829 */     mythicPlayer.setMythicParty(new MythicParty(target));
/* 830 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean removeFromParty(Player target) {
/* 836 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 838 */     MythicPlayer mythicPlayer = getMythicPlayer(target);
/* 839 */     MythicParty party = mythicPlayer.getMythicParty();
/*     */     
/* 841 */     if (party == null) return false;
/*     */     
/* 843 */     party.removePlayer(mythicPlayer.getPlayer());
/* 844 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean disbandParty(MythicParty party) {
/* 850 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 852 */     party.disband();
/* 853 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean disbandParty(Player target) {
/* 859 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 861 */     MythicPlayer mythicPlayer = getMythicPlayer(target);
/* 862 */     MythicParty party = mythicPlayer.getMythicParty();
/*     */     
/* 864 */     if (party == null) return false; 
/* 865 */     party.disband();
/* 866 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean inviteToParty(Player source, Player target) {
/* 872 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 874 */     MythicPlayer sourceMythicPlayer = getMythicPlayer(source);
/*     */     
/* 876 */     if (!sourceMythicPlayer.hasParty()) return false;
/*     */     
/* 878 */     MythicPlayer targetMythicPlayer = getMythicPlayer(target);
/*     */     
/* 880 */     if (targetMythicPlayer.hasParty()) return false;
/*     */     
/* 882 */     targetMythicPlayer.setInviteFrom(source);
/* 883 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean acceptPartyInvite(Player target) {
/* 889 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 891 */     MythicPlayer mythicPlayer = getMythicPlayer(target);
/*     */     
/* 893 */     if (mythicPlayer.hasParty()) return false; 
/* 894 */     if (mythicPlayer.getInviteFrom() == null) return false;
/*     */     
/* 896 */     Player inviter = mythicPlayer.getInviteFrom();
/* 897 */     MythicPlayer inviterMythicPlayer = getMythicPlayer(inviter);
/* 898 */     MythicParty party = inviterMythicPlayer.getMythicParty();
/*     */     
/* 900 */     if (party == null) return false;
/*     */     
/* 902 */     party.addPlayer(mythicPlayer.getPlayer());
/* 903 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean declinePartyInvite(Player target) {
/* 909 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 911 */     MythicPlayer mythicPlayer = getMythicPlayer(target);
/*     */     
/* 913 */     if (mythicPlayer.hasParty()) return false; 
/* 914 */     if (mythicPlayer.getInviteFrom() == null) return false;
/*     */     
/* 916 */     mythicPlayer.setInviteFrom(null);
/* 917 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean setPartyLeader(Player player) {
/* 923 */     if (!checkIfInternalPartySystem()) return false;
/*     */     
/* 925 */     MythicPlayer mythicPlayer = getMythicPlayer(player);
/*     */     
/* 927 */     if (!mythicPlayer.hasParty()) return false;
/*     */     
/* 929 */     MythicParty party = mythicPlayer.getMythicParty();
/* 930 */     party.setMythicLeader(player);
/* 931 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public MythicParty getParty(Player player) {
/* 937 */     if (!checkIfInternalPartySystem()) return null;
/*     */     
/* 939 */     MythicPlayer mythicPlayer = getMythicPlayer(player);
/*     */     
/* 941 */     if (!mythicPlayer.hasParty()) return null;
/*     */     
/* 943 */     return mythicPlayer.getMythicParty();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String isPartyQueuedForDungeon(Player player) {
/* 950 */     MythicPlayer mythicPlayer = getMythicPlayer(player);
/* 951 */     QueueData queue = this.queueManager.getQueue(mythicPlayer);
/* 952 */     if (queue == null) return null; 
/* 953 */     return queue.getDungeon().getWorldName();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String isPartyQueuedForDungeon(IDungeonParty party) {
/* 960 */     if (party.getLeader() == null) return null; 
/* 961 */     MythicPlayer mythicPlayer = getMythicPlayer(party.getLeader().getUniqueId());
/* 962 */     QueueData queue = this.queueManager.getQueue(mythicPlayer);
/* 963 */     if (queue == null) return null; 
/* 964 */     return queue.getDungeon().getWorldName();
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Downloads\extract\MythicDungeons-2.0.1-SNAPSHOT.jar!\net\playavalon\mythicdungeons\MythicDungeons.class
 * Java compiler version: 21 (65.0)
 * JD-Core Version:       1.1.3
 */