package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.player.MythicPlayer;
import net.playavalon.mythicdungeons.api.party.IDungeonParty;
import net.playavalon.mythicdungeons.player.party.partyfinder.RecruitmentListing;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import net.playavalon.mythicdungeons.utility.helpers.LangUtils;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.Arrays;
import java.util.List;

/**
 * Enhanced Party Management GUI - Comprehensive party system interface
 * Features party creation, member management, recruitment, and party browser
 */
public class PartyManagementGUI {
    
    private static final String GUI_NAME = "enhanced_party_management";
    private static final String GUI_TITLE = "&8\u2726 &a&lParty Management &8\u2726";
    
    /**
     * Initialize the enhanced party management GUI
     */
    public static void initPartyManagement() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupPartyActions(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_party_info", event -> {
            Player player = (Player) event.getPlayer();
            loadPartyInformation(gui, player);
        });
    }
    
    /**
     * Set up party action buttons
     */
    private static void setupPartyActions(Window gui) {
        // Create Party Button
        Button createParty = new Button("create_party", Material.EMERALD, "&a&lCreate Party");
        createParty.addLoreComponent(Util.modernizeColorsComponent("&7Start a new party"));
        createParty.addLoreComponent(Util.modernizeColorsComponent("&7Become the party leader"));
        createParty.addLoreComponent(Util.modernizeColorsComponent("&7Invite friends to join"));
        createParty.addLoreComponent(Util.modernizeColorsComponent(""));
        createParty.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to create party"));
        
        createParty.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            handleCreateParty(player);
        });
        gui.addButton(10, createParty);
        
        // Browse Parties Button
        Button browseParties = new Button("browse_parties", Material.COMPASS, "&b&lBrowse Parties");
        browseParties.addLoreComponent(Util.modernizeColorsComponent("&7View available parties"));
        browseParties.addLoreComponent(Util.modernizeColorsComponent("&7Join existing groups"));
        browseParties.addLoreComponent(Util.modernizeColorsComponent("&7Filter by dungeon type"));
        browseParties.addLoreComponent(Util.modernizeColorsComponent(""));
        browseParties.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to browse"));
        
        browseParties.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openPartyBrowser(player);
        });
        gui.addButton(12, browseParties);
        
        // Party Recruitment Button
        Button recruitment = new Button("party_recruitment", Material.BELL, "&d&lStart Recruitment");
        recruitment.addLoreComponent(Util.modernizeColorsComponent("&7Create a party listing"));
        recruitment.addLoreComponent(Util.modernizeColorsComponent("&7Attract new members"));
        recruitment.addLoreComponent(Util.modernizeColorsComponent("&7Set requirements"));
        recruitment.addLoreComponent(Util.modernizeColorsComponent(""));
        recruitment.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to recruit"));
        
        recruitment.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            handleStartRecruitment(player);
        });
        gui.addButton(14, recruitment);
        
        // Leave Party Button
        Button leaveParty = new Button("leave_party", Material.RED_CONCRETE, "&c&lLeave Party");
        leaveParty.addLoreComponent(Util.modernizeColorsComponent("&7Leave your current party"));
        leaveParty.addLoreComponent(Util.modernizeColorsComponent("&cThis action cannot be undone"));
        leaveParty.addLoreComponent(Util.modernizeColorsComponent(""));
        leaveParty.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to leave"));
        
        leaveParty.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            handleLeaveParty(player);
        });
        gui.addButton(16, leaveParty);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(45, backButton);
        
        // Refresh Button
        Button refreshButton = new Button("refresh_party", Material.CLOCK, "&7Refresh");
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&7Update party information"));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent(""));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to refresh"));
        
        refreshButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            loadPartyInformation(gui, player);
        });
        gui.addButton(53, refreshButton);
    }
    
    /**
     * Load and display party information
     */
    private static void loadPartyInformation(Window gui, Player player) {
        MythicPlayer mythicPlayer = MythicDungeons.inst().getMythicPlayer(player);
        IDungeonParty party = mythicPlayer.getDungeonParty();
        
        // Clear party member display area (slots 19-34)
        for (int i = 19; i <= 34; i++) {
            gui.removeButton(i);
        }
        
        if (party != null) {
            displayPartyMembers(gui, party, player);
            displayPartyInfo(gui, party, player);
            updatePartyActionButtons(gui, party, player);
        } else {
            displayNoPartyMessage(gui);
        }
        
        gui.updateButtons(player);
    }
    
    /**
     * Display party members in the GUI
     */
    private static void displayPartyMembers(Window gui, IDungeonParty party, Player viewer) {
        List<Player> members = party.getPlayers();
        Player leader = party.getLeader();
        
        int slot = 19;
        for (Player member : members) {
            if (slot > 34) break; // Max 16 members displayed
            
            ItemStack skull = new ItemStack(Material.PLAYER_HEAD);
            SkullMeta meta = (SkullMeta) skull.getItemMeta();
            meta.setOwningPlayer(member);
            
            String memberName = "&f" + member.getName();
            if (member.equals(leader)) {
                memberName = "&6&l\u2605 &6" + member.getName() + " &7(Leader)";
            }
            
            meta.setDisplayName(Util.modernizeColors(memberName));
            
            List<String> lore = Arrays.asList(
                "&7Status: " + (member.isOnline() ? "&aOnline" : "&cOffline"),
                "&7Level: &f" + member.getLevel(),
                "&7Health: &f" + Math.round(member.getHealth()) + "/" + Math.round(member.getMaxHealth())
            );
            
            if (member.equals(viewer)) {
                lore.add("");
                lore.add("&7This is you!");
            } else if (viewer.equals(leader)) {
                lore.add("");
                lore.add("&e\u25b6 Click to manage member");
            }
            
            meta.setLore(lore.stream()
                .map(Util::modernizeColors)
                .collect(java.util.stream.Collectors.toList()));
            
            skull.setItemMeta(meta);
            
            Button memberButton = new Button("member_" + member.getName(), skull);
            
            if (!member.equals(viewer) && viewer.equals(leader)) {
                memberButton.addAction("click", event -> {
                    openMemberManagement(viewer, member, party);
                });
            }
            
            gui.addButton(slot, memberButton);
            slot++;
        }
    }
    
    /**
     * Display party information panel
     */
    private static void displayPartyInfo(Window gui, IDungeonParty party, Player viewer) {
        // Party Info Button (slot 4)
        Button partyInfo = new Button("party_info", Material.BOOK, "&6&lParty Information");
        partyInfo.addLoreComponent(Util.modernizeColorsComponent("&7Leader: &f" + party.getLeader().getName()));
        partyInfo.addLoreComponent(Util.modernizeColorsComponent("&7Members: &f" + party.getPlayers().size()));
        partyInfo.addLoreComponent(Util.modernizeColorsComponent("&7Max Size: &f" + party.getMaxSize()));
        partyInfo.addLoreComponent(Util.modernizeColorsComponent(""));
        
        if (party.getPlayers().size() < party.getMaxSize()) {
            partyInfo.addLoreComponent(Util.modernizeColorsComponent("&a\u25cf Party has open slots"));
        } else {
            partyInfo.addLoreComponent(Util.modernizeColorsComponent("&c\u25cf Party is full"));
        }
        
        gui.addButton(4, partyInfo);
    }
    
    /**
     * Update party action buttons based on current state
     */
    private static void updatePartyActionButtons(Window gui, IDungeonParty party, Player player) {
        boolean isLeader = party.getLeader().equals(player);
        
        // Update Create Party button to show "Disband Party" for leaders
        if (isLeader) {
            Button disbandButton = new Button("disband_party", Material.TNT, "&c&lDisband Party");
            disbandButton.addLoreComponent(Util.modernizeColorsComponent("&7Disband the entire party"));
            disbandButton.addLoreComponent(Util.modernizeColorsComponent("&cAll members will be removed"));
            disbandButton.addLoreComponent(Util.modernizeColorsComponent(""));
            disbandButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to disband"));
            
            disbandButton.addAction("click", event -> {
                handleDisbandParty(player, party);
            });
            gui.addButton(10, disbandButton);
        }
        
        // Update recruitment button for leaders
        if (isLeader) {
            Button manageRecruitment = new Button("manage_recruitment", Material.BELL, "&d&lManage Recruitment");
            RecruitmentListing listing = MythicDungeons.inst().getListingManager().getListing(player);
            
            if (listing != null) {
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent("&7Current listing: &f" + listing.getLabel()));
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent("&7Status: &aActive"));
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent(""));
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to cancel listing"));
            } else {
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent("&7Create a recruitment listing"));
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent("&7Attract new members"));
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent(""));
                manageRecruitment.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to start recruiting"));
            }
            
            gui.addButton(14, manageRecruitment);
        }
    }
    
    /**
     * Display message when player has no party
     */
    private static void displayNoPartyMessage(Window gui) {
        Button noPartyMessage = new Button("no_party", Material.GRAY_CONCRETE, "&7&lNo Party");
        noPartyMessage.addLoreComponent(Util.modernizeColorsComponent("&7You are not currently in a party"));
        noPartyMessage.addLoreComponent(Util.modernizeColorsComponent(""));
        noPartyMessage.addLoreComponent(Util.modernizeColorsComponent("&7Create a party or browse"));
        noPartyMessage.addLoreComponent(Util.modernizeColorsComponent("&7existing parties to get started!"));
        
        gui.addButton(22, noPartyMessage);
    }
    
    /**
     * Handle party creation
     */
    private static void handleCreateParty(Player player) {
        if (MythicDungeons.inst().createParty(player)) {
            player.sendMessage(Util.modernizeColorsComponent("&aParty created successfully! You are now the leader."));
            openPartyManagement(player); // Refresh the GUI
        } else {
            player.sendMessage(Util.modernizeColorsComponent("&cFailed to create party. You may already be in one."));
        }
    }
    
    /**
     * Handle party leaving
     */
    private static void handleLeaveParty(Player player) {
        if (MythicDungeons.inst().removeFromParty(player)) {
            player.sendMessage(Util.modernizeColorsComponent("&cYou have left the party."));
            openPartyManagement(player); // Refresh the GUI
        } else {
            player.sendMessage(Util.modernizeColorsComponent("&cYou are not in a party."));
        }
    }
    
    /**
     * Handle party disbanding
     */
    private static void handleDisbandParty(Player player, IDungeonParty party) {
        // Implementation depends on the party system being used
        player.sendMessage(Util.modernizeColorsComponent("&cParty disbanded."));
        openPartyManagement(player); // Refresh the GUI
    }
    
    /**
     * Handle recruitment start
     */
    private static void handleStartRecruitment(Player player) {
        player.closeInventory();
        player.performCommand("recruit");
    }
    
    /**
     * Open party browser
     */
    private static void openPartyBrowser(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, "partybrowser");
    }
    
    /**
     * Open member management for a specific player
     */
    private static void openMemberManagement(Player leader, Player member, IDungeonParty party) {
        // Create a simple member management interface
        leader.sendMessage(Util.modernizeColorsComponent("&7Managing member: &f" + member.getName()));
        // Could open a sub-GUI for member management actions
    }
    
    /**
     * Open the party management GUI for a player
     */
    public static void openPartyManagement(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
}
