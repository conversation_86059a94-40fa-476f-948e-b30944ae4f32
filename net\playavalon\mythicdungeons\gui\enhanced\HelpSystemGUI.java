package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.Arrays;

/**
 * Enhanced Help System GUI - Interactive help and tutorial system
 * Features tutorials, command reference, tips, and troubleshooting guides
 */
public class HelpSystemGUI {
    
    private static final String GUI_NAME = "enhanced_help";
    private static final String GUI_TITLE = "&8\u2726 &f&lHelp & Tutorials &8\u2726";
    
    /**
     * Initialize the enhanced help system GUI
     */
    public static void initHelpSystem() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupHelpCategories(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_help", event -> {
            Player player = (Player) event.getPlayer();
            loadHelpContent(gui, player);
        });
    }
    
    /**
     * Set up help category buttons
     */
    private static void setupHelpCategories(Window gui) {
        // Getting Started Tutorial
        Button gettingStarted = new Button("getting_started", Material.COMPASS, "&a&lGetting Started");
        gettingStarted.addLoreComponent(Util.modernizeColorsComponent("&7Learn the basics of dungeons"));
        gettingStarted.addLoreComponent(Util.modernizeColorsComponent("&7How to join your first dungeon"));
        gettingStarted.addLoreComponent(Util.modernizeColorsComponent("&7Understanding the interface"));
        gettingStarted.addLoreComponent(Util.modernizeColorsComponent(""));
        gettingStarted.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to start tutorial"));
        
        gettingStarted.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            startGettingStartedTutorial(player);
        });
        gui.addButton(10, gettingStarted);
        
        // Command Reference
        Button commandReference = new Button("command_reference", Material.WRITABLE_BOOK, "&b&lCommand Reference");
        commandReference.addLoreComponent(Util.modernizeColorsComponent("&7Complete list of commands"));
        commandReference.addLoreComponent(Util.modernizeColorsComponent("&7Usage examples and syntax"));
        commandReference.addLoreComponent(Util.modernizeColorsComponent("&7Permission requirements"));
        commandReference.addLoreComponent(Util.modernizeColorsComponent(""));
        commandReference.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view commands"));
        
        commandReference.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showCommandReference(player);
        });
        gui.addButton(12, commandReference);
        
        // Party System Guide
        Button partyGuide = new Button("party_guide", Material.PLAYER_HEAD, "&d&lParty System Guide");
        partyGuide.addLoreComponent(Util.modernizeColorsComponent("&7How to create and manage parties"));
        partyGuide.addLoreComponent(Util.modernizeColorsComponent("&7Recruitment and joining"));
        partyGuide.addLoreComponent(Util.modernizeColorsComponent("&7Party roles and permissions"));
        partyGuide.addLoreComponent(Util.modernizeColorsComponent(""));
        partyGuide.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to learn about parties"));
        
        partyGuide.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showPartyGuide(player);
        });
        gui.addButton(14, partyGuide);
        
        // Dungeon Types Guide
        Button dungeonGuide = new Button("dungeon_guide", Material.ENDER_CHEST, "&6&lDungeon Types Guide");
        dungeonGuide.addLoreComponent(Util.modernizeColorsComponent("&7Understanding dungeon types"));
        dungeonGuide.addLoreComponent(Util.modernizeColorsComponent("&7Difficulty levels and scaling"));
        dungeonGuide.addLoreComponent(Util.modernizeColorsComponent("&7Rewards and progression"));
        dungeonGuide.addLoreComponent(Util.modernizeColorsComponent(""));
        dungeonGuide.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to learn about dungeons"));
        
        dungeonGuide.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showDungeonGuide(player);
        });
        gui.addButton(16, dungeonGuide);
        
        // Tips & Tricks
        Button tipsAndTricks = new Button("tips_tricks", Material.LIGHT_BULB, "&e&lTips & Tricks");
        tipsAndTricks.addLoreComponent(Util.modernizeColorsComponent("&7Advanced strategies"));
        tipsAndTricks.addLoreComponent(Util.modernizeColorsComponent("&7Optimization tips"));
        tipsAndTricks.addLoreComponent(Util.modernizeColorsComponent("&7Hidden features"));
        tipsAndTricks.addLoreComponent(Util.modernizeColorsComponent(""));
        tipsAndTricks.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view tips"));
        
        tipsAndTricks.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showTipsAndTricks(player);
        });
        gui.addButton(28, tipsAndTricks);
        
        // Troubleshooting
        Button troubleshooting = new Button("troubleshooting", Material.REDSTONE_TORCH, "&c&lTroubleshooting");
        troubleshooting.addLoreComponent(Util.modernizeColorsComponent("&7Common issues and solutions"));
        troubleshooting.addLoreComponent(Util.modernizeColorsComponent("&7Error message explanations"));
        troubleshooting.addLoreComponent(Util.modernizeColorsComponent("&7How to get support"));
        troubleshooting.addLoreComponent(Util.modernizeColorsComponent(""));
        troubleshooting.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click for troubleshooting"));
        
        troubleshooting.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showTroubleshooting(player);
        });
        gui.addButton(30, troubleshooting);
        
        // FAQ
        Button faq = new Button("faq", Material.KNOWLEDGE_BOOK, "&9&lFrequently Asked Questions");
        faq.addLoreComponent(Util.modernizeColorsComponent("&7Common questions and answers"));
        faq.addLoreComponent(Util.modernizeColorsComponent("&7Quick solutions"));
        faq.addLoreComponent(Util.modernizeColorsComponent("&7Community wisdom"));
        faq.addLoreComponent(Util.modernizeColorsComponent(""));
        faq.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view FAQ"));
        
        faq.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showFAQ(player);
        });
        gui.addButton(32, faq);
        
        // Video Tutorials
        Button videoTutorials = new Button("video_tutorials", Material.ITEM_FRAME, "&5&lVideo Tutorials");
        videoTutorials.addLoreComponent(Util.modernizeColorsComponent("&7Links to video guides"));
        videoTutorials.addLoreComponent(Util.modernizeColorsComponent("&7Step-by-step walkthroughs"));
        videoTutorials.addLoreComponent(Util.modernizeColorsComponent("&7Visual learning resources"));
        videoTutorials.addLoreComponent(Util.modernizeColorsComponent(""));
        videoTutorials.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view tutorials"));
        
        videoTutorials.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showVideoTutorials(player);
        });
        gui.addButton(34, videoTutorials);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(45, backButton);
        
        // Quick Help Button
        Button quickHelp = new Button("quick_help", Material.EMERALD, "&a&lQuick Help");
        quickHelp.addLoreComponent(Util.modernizeColorsComponent("&7Essential commands"));
        quickHelp.addLoreComponent(Util.modernizeColorsComponent("&7Basic controls"));
        quickHelp.addLoreComponent(Util.modernizeColorsComponent(""));
        quickHelp.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click for quick reference"));
        
        quickHelp.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showQuickHelp(player);
        });
        gui.addButton(49, quickHelp);
        
        // Contact Support Button
        Button contactSupport = new Button("contact_support", Material.BELL, "&c&lContact Support");
        contactSupport.addLoreComponent(Util.modernizeColorsComponent("&7Get help from staff"));
        contactSupport.addLoreComponent(Util.modernizeColorsComponent("&7Report bugs or issues"));
        contactSupport.addLoreComponent(Util.modernizeColorsComponent(""));
        contactSupport.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to contact support"));
        
        contactSupport.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            contactSupport(player);
        });
        gui.addButton(53, contactSupport);
    }
    
    /**
     * Load help content
     */
    private static void loadHelpContent(Window gui, Player player) {
        // Display welcome message in center
        displayWelcomeMessage(gui, player);
        
        gui.updateButtons(player);
    }
    
    /**
     * Display welcome message
     */
    private static void displayWelcomeMessage(Window gui, Player player) {
        // Welcome Message (slot 22)
        Button welcomeMessage = new Button("welcome_message", Material.BOOK, "&f&lWelcome to Help Center");
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent("&7Hello, &f" + player.getName() + "&7!"));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent(""));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent("&7This help center contains"));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent("&7everything you need to know"));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent("&7about using Mythic Dungeons."));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent(""));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent("&7Select a category above"));
        welcomeMessage.addLoreComponent(Util.modernizeColorsComponent("&7to get started!"));
        
        gui.addButton(22, welcomeMessage);
    }
    
    /**
     * Start getting started tutorial
     */
    private static void startGettingStartedTutorial(Player player) {
        player.closeInventory();
        player.sendMessage(Util.modernizeColorsComponent("&a&l=== Getting Started Tutorial ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Welcome to Mythic Dungeons! Here's how to get started:"));
        player.sendMessage(Util.modernizeColorsComponent(""));
        player.sendMessage(Util.modernizeColorsComponent("&61. &eUse &f/md &eto open the main menu"));
        player.sendMessage(Util.modernizeColorsComponent("&62. &eBrowse available dungeons"));
        player.sendMessage(Util.modernizeColorsComponent("&63. &eCreate or join a party (optional)"));
        player.sendMessage(Util.modernizeColorsComponent("&64. &eSelect a dungeon and difficulty"));
        player.sendMessage(Util.modernizeColorsComponent("&65. &eEnjoy your adventure!"));
        player.sendMessage(Util.modernizeColorsComponent(""));
        player.sendMessage(Util.modernizeColorsComponent("&7Type &f/md help &7for more information."));
    }
    
    /**
     * Show command reference
     */
    private static void showCommandReference(Player player) {
        player.closeInventory();
        player.sendMessage(Util.modernizeColorsComponent("&b&l=== Command Reference ==="));
        player.sendMessage(Util.modernizeColorsComponent("&f/md &7- Open main dashboard"));
        player.sendMessage(Util.modernizeColorsComponent("&f/md play <dungeon> &7- Join a dungeon"));
        player.sendMessage(Util.modernizeColorsComponent("&f/leave &7- Leave current dungeon"));
        player.sendMessage(Util.modernizeColorsComponent("&f/ready &7- Ready up for dungeon"));
        player.sendMessage(Util.modernizeColorsComponent("&f/party &7- Manage your party"));
        player.sendMessage(Util.modernizeColorsComponent("&f/recruit &7- Start party recruitment"));
        player.sendMessage(Util.modernizeColorsComponent("&f/rewards &7- View your rewards"));
        player.sendMessage(Util.modernizeColorsComponent("&f/stuck &7- Teleport to checkpoint"));
    }
    
    /**
     * Show party guide
     */
    private static void showPartyGuide(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&d&l=== Party System Guide ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Parties allow you to play with friends!"));
        // Add more party guide content
    }
    
    /**
     * Show dungeon guide
     */
    private static void showDungeonGuide(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&6&l=== Dungeon Types Guide ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Learn about different dungeon types:"));
        // Add more dungeon guide content
    }
    
    /**
     * Show tips and tricks
     */
    private static void showTipsAndTricks(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&e&l=== Tips & Tricks ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Pro tips for dungeon mastery:"));
        // Add tips and tricks content
    }
    
    /**
     * Show troubleshooting
     */
    private static void showTroubleshooting(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&c&l=== Troubleshooting ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Common issues and solutions:"));
        // Add troubleshooting content
    }
    
    /**
     * Show FAQ
     */
    private static void showFAQ(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&9&l=== Frequently Asked Questions ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Q: How do I join a dungeon?"));
        player.sendMessage(Util.modernizeColorsComponent("&fA: Use /md play <dungeon> or browse through the GUI"));
        // Add more FAQ content
    }
    
    /**
     * Show video tutorials
     */
    private static void showVideoTutorials(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&5&l=== Video Tutorials ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Check out these helpful video guides:"));
        // Add video tutorial links
    }
    
    /**
     * Show quick help
     */
    private static void showQuickHelp(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&a&l=== Quick Help ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7Essential commands: &f/md, /leave, /ready, /party"));
        player.sendMessage(Util.modernizeColorsComponent("&7Need help? Use &f/md help &7or contact staff"));
    }
    
    /**
     * Contact support
     */
    private static void contactSupport(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&c&l=== Contact Support ==="));
        player.sendMessage(Util.modernizeColorsComponent("&7For assistance, please contact server staff"));
        player.sendMessage(Util.modernizeColorsComponent("&7or visit our support channels."));
    }
    
    /**
     * Open the help system GUI for a player
     */
    public static void openHelpSystem(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
}
