package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.player.MythicPlayer;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.Arrays;

/**
 * Enhanced Player Statistics GUI - Comprehensive player progress tracking
 * Features achievements, leaderboards, progress tracking, and detailed statistics
 */
public class PlayerStatsGUI {
    
    private static final String GUI_NAME = "enhanced_player_stats";
    private static final String GUI_TITLE = "&8\u2726 &b&lPlayer Statistics &8\u2726";
    
    /**
     * Initialize the enhanced player statistics GUI
     */
    public static void initPlayerStats() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupStatCategories(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_player_stats", event -> {
            Player player = (Player) event.getPlayer();
            loadPlayerStatistics(gui, player);
        });
    }
    
    /**
     * Set up statistic category buttons
     */
    private static void setupStatCategories(Window gui) {
        // General Statistics
        Button generalStats = new Button("general_stats", Material.BOOK, "&6&lGeneral Statistics");
        generalStats.addLoreComponent(Util.modernizeColorsComponent("&7View overall dungeon progress"));
        generalStats.addLoreComponent(Util.modernizeColorsComponent("&7Completion rates and times"));
        generalStats.addLoreComponent(Util.modernizeColorsComponent("&7Deaths and survival stats"));
        generalStats.addLoreComponent(Util.modernizeColorsComponent(""));
        generalStats.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view details"));
        
        generalStats.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showGeneralStats(player);
        });
        gui.addButton(10, generalStats);
        
        // Achievements
        Button achievements = new Button("achievements", Material.GOLD_INGOT, "&e&lAchievements");
        achievements.addLoreComponent(Util.modernizeColorsComponent("&7Browse your achievements"));
        achievements.addLoreComponent(Util.modernizeColorsComponent("&7Track progress on goals"));
        achievements.addLoreComponent(Util.modernizeColorsComponent("&7View completion rewards"));
        achievements.addLoreComponent(Util.modernizeColorsComponent(""));
        achievements.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view achievements"));
        
        achievements.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showAchievements(player);
        });
        gui.addButton(12, achievements);
        
        // Leaderboards
        Button leaderboards = new Button("leaderboards", Material.DIAMOND, "&b&lLeaderboards");
        leaderboards.addLoreComponent(Util.modernizeColorsComponent("&7Compare with other players"));
        leaderboards.addLoreComponent(Util.modernizeColorsComponent("&7Server-wide rankings"));
        leaderboards.addLoreComponent(Util.modernizeColorsComponent("&7Seasonal competitions"));
        leaderboards.addLoreComponent(Util.modernizeColorsComponent(""));
        leaderboards.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view rankings"));
        
        leaderboards.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showLeaderboards(player);
        });
        gui.addButton(14, leaderboards);
        
        // Progress Tracking
        Button progressTracking = new Button("progress_tracking", Material.MAP, "&d&lProgress Tracking");
        progressTracking.addLoreComponent(Util.modernizeColorsComponent("&7Track dungeon completion"));
        progressTracking.addLoreComponent(Util.modernizeColorsComponent("&7View difficulty progression"));
        progressTracking.addLoreComponent(Util.modernizeColorsComponent("&7Monitor improvement"));
        progressTracking.addLoreComponent(Util.modernizeColorsComponent(""));
        progressTracking.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view progress"));
        
        progressTracking.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showProgressTracking(player);
        });
        gui.addButton(16, progressTracking);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(45, backButton);
        
        // Refresh Button
        Button refreshButton = new Button("refresh_stats", Material.CLOCK, "&7Refresh");
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&7Update statistics"));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent(""));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to refresh"));
        
        refreshButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            loadPlayerStatistics(gui, player);
        });
        gui.addButton(53, refreshButton);
    }
    
    /**
     * Load and display player statistics
     */
    private static void loadPlayerStatistics(Window gui, Player player) {
        MythicPlayer mythicPlayer = MythicDungeons.inst().getMythicPlayer(player);
        
        // Display current statistics overview
        displayStatsOverview(gui, player, mythicPlayer);
        
        gui.updateButtons(player);
    }
    
    /**
     * Display statistics overview in the center area
     */
    private static void displayStatsOverview(Window gui, Player player, MythicPlayer mythicPlayer) {
        // Clear center area (slots 19-34)
        for (int i = 19; i <= 34; i++) {
            gui.removeButton(i);
        }
        
        // Player Overview (slot 22)
        Button playerOverview = new Button("player_overview", Material.PLAYER_HEAD, "&f&l" + player.getName());
        playerOverview.addLoreComponent(Util.modernizeColorsComponent("&7Level: &f" + player.getLevel()));
        playerOverview.addLoreComponent(Util.modernizeColorsComponent("&7Dungeons Completed: &f0")); // Placeholder
        playerOverview.addLoreComponent(Util.modernizeColorsComponent("&7Total Deaths: &f0")); // Placeholder
        playerOverview.addLoreComponent(Util.modernizeColorsComponent("&7Best Time: &fN/A")); // Placeholder
        playerOverview.addLoreComponent(Util.modernizeColorsComponent(""));
        playerOverview.addLoreComponent(Util.modernizeColorsComponent("&7Select a category above"));
        playerOverview.addLoreComponent(Util.modernizeColorsComponent("&7to view detailed statistics"));
        
        gui.addButton(22, playerOverview);
        
        // Quick Stats
        displayQuickStats(gui, player, mythicPlayer);
    }
    
    /**
     * Display quick statistics around the overview
     */
    private static void displayQuickStats(Window gui, Player player, MythicPlayer mythicPlayer) {
        // Dungeons Completed (slot 20)
        Button dungeonsCompleted = new Button("quick_dungeons", Material.EMERALD, "&a&lDungeons Completed");
        dungeonsCompleted.addLoreComponent(Util.modernizeColorsComponent("&7Total: &f0"));
        dungeonsCompleted.addLoreComponent(Util.modernizeColorsComponent("&7This Week: &f0"));
        dungeonsCompleted.addLoreComponent(Util.modernizeColorsComponent("&7Success Rate: &f0%"));
        gui.addButton(20, dungeonsCompleted);
        
        // Deaths (slot 21)
        Button deaths = new Button("quick_deaths", Material.SKELETON_SKULL, "&c&lDeaths");
        deaths.addLoreComponent(Util.modernizeColorsComponent("&7Total: &f0"));
        deaths.addLoreComponent(Util.modernizeColorsComponent("&7This Week: &f0"));
        deaths.addLoreComponent(Util.modernizeColorsComponent("&7Survival Rate: &f100%"));
        gui.addButton(21, deaths);
        
        // Playtime (slot 23)
        Button playtime = new Button("quick_playtime", Material.CLOCK, "&6&lPlaytime");
        playtime.addLoreComponent(Util.modernizeColorsComponent("&7Total: &f0h 0m"));
        playtime.addLoreComponent(Util.modernizeColorsComponent("&7This Session: &f0h 0m"));
        playtime.addLoreComponent(Util.modernizeColorsComponent("&7Average: &f0h 0m"));
        gui.addButton(23, playtime);
        
        // Achievements (slot 24)
        Button achievementProgress = new Button("quick_achievements", Material.GOLD_INGOT, "&e&lAchievements");
        achievementProgress.addLoreComponent(Util.modernizeColorsComponent("&7Unlocked: &f0/0"));
        achievementProgress.addLoreComponent(Util.modernizeColorsComponent("&7Progress: &f0%"));
        achievementProgress.addLoreComponent(Util.modernizeColorsComponent("&7Points: &f0"));
        gui.addButton(24, achievementProgress);
    }
    
    /**
     * Show general statistics
     */
    private static void showGeneralStats(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening general statistics..."));
        // Implementation for detailed general stats view
    }
    
    /**
     * Show achievements
     */
    private static void showAchievements(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening achievements..."));
        // Implementation for achievements view
    }
    
    /**
     * Show leaderboards
     */
    private static void showLeaderboards(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening leaderboards..."));
        // Implementation for leaderboards view
    }
    
    /**
     * Show progress tracking
     */
    private static void showProgressTracking(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening progress tracking..."));
        // Implementation for progress tracking view
    }
    
    /**
     * Open the player statistics GUI for a player
     */
    public static void openPlayerStats(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
}
