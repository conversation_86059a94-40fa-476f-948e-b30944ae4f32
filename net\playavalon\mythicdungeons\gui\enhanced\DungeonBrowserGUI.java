package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.api.parents.dungeons.AbstractDungeon;
import net.playavalon.mythicdungeons.dungeons.DungeonDifficulty;
import net.playavalon.mythicdungeons.player.MythicPlayer;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import net.playavalon.mythicdungeons.utility.helpers.LangUtils;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Enhanced Dungeon Browser GUI - Advanced dungeon selection interface
 * Features filtering, search, previews, and detailed dungeon information
 */
public class DungeonBrowserGUI {
    
    private static final String GUI_NAME = "enhanced_dungeon_browser";
    private static final String GUI_TITLE = "&8\u2726 &6&lDungeon Browser &8\u2726";
    
    // Filter states for each player
    private static final Map<UUID, DungeonFilter> playerFilters = new HashMap<>();
    
    /**
     * Initialize the enhanced dungeon browser GUI
     */
    public static void initDungeonBrowser() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupFilterButtons(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_dungeons", event -> {
            Player player = (Player) event.getPlayer();
            loadDungeonList(gui, player);
        });
    }
    
    /**
     * Set up filter and search buttons
     */
    private static void setupFilterButtons(Window gui) {
        // Difficulty Filter Button
        Button difficultyFilter = new Button("filter_difficulty", Material.DIAMOND_SWORD, "&b&lDifficulty Filter");
        difficultyFilter.addLoreComponent(Util.modernizeColorsComponent("&7Filter dungeons by difficulty"));
        difficultyFilter.addLoreComponent(Util.modernizeColorsComponent("&7Current: &fAll Difficulties"));
        difficultyFilter.addLoreComponent(Util.modernizeColorsComponent(""));
        difficultyFilter.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to change filter"));
        
        difficultyFilter.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            cycleDifficultyFilter(player);
            loadDungeonList(gui, player);
        });
        gui.addButton(45, difficultyFilter);
        
        // Type Filter Button
        Button typeFilter = new Button("filter_type", Material.COMPASS, "&a&lType Filter");
        typeFilter.addLoreComponent(Util.modernizeColorsComponent("&7Filter dungeons by type"));
        typeFilter.addLoreComponent(Util.modernizeColorsComponent("&7Current: &fAll Types"));
        typeFilter.addLoreComponent(Util.modernizeColorsComponent(""));
        typeFilter.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to change filter"));
        
        typeFilter.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            cycleTypeFilter(player);
            loadDungeonList(gui, player);
        });
        gui.addButton(46, typeFilter);
        
        // Status Filter Button
        Button statusFilter = new Button("filter_status", Material.REDSTONE_TORCH, "&c&lStatus Filter");
        statusFilter.addLoreComponent(Util.modernizeColorsComponent("&7Filter by availability"));
        statusFilter.addLoreComponent(Util.modernizeColorsComponent("&7Current: &fAll Dungeons"));
        statusFilter.addLoreComponent(Util.modernizeColorsComponent(""));
        statusFilter.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to change filter"));
        
        statusFilter.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            cycleStatusFilter(player);
            loadDungeonList(gui, player);
        });
        gui.addButton(47, statusFilter);
        
        // Clear Filters Button
        Button clearFilters = new Button("clear_filters", Material.BARRIER, "&7Clear Filters");
        clearFilters.addLoreComponent(Util.modernizeColorsComponent("&7Reset all filters"));
        clearFilters.addLoreComponent(Util.modernizeColorsComponent(""));
        clearFilters.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to clear"));
        
        clearFilters.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            clearFilters(player);
            loadDungeonList(gui, player);
        });
        gui.addButton(48, clearFilters);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(49, backButton);
        
        // Refresh Button
        Button refreshButton = new Button("refresh_browser", Material.CLOCK, "&7Refresh");
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&7Update dungeon list"));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent(""));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to refresh"));
        
        refreshButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            loadDungeonList(gui, player);
        });
        gui.addButton(53, refreshButton);
    }
    
    /**
     * Load and display the filtered dungeon list
     */
    private static void loadDungeonList(Window gui, Player player) {
        // Clear existing dungeon buttons (slots 0-44)
        for (int i = 0; i < 45; i++) {
            gui.removeButton(i);
        }
        
        DungeonFilter filter = playerFilters.getOrDefault(player.getUniqueId(), new DungeonFilter());
        Collection<AbstractDungeon> allDungeons = MythicDungeons.inst().getDungeons().getAll();
        
        List<AbstractDungeon> filteredDungeons = allDungeons.stream()
            .filter(dungeon -> matchesFilter(dungeon, filter))
            .sorted(Comparator.comparing(AbstractDungeon::getDisplayName))
            .collect(Collectors.toList());
        
        // Display dungeons (max 45 slots)
        int slot = 0;
        for (AbstractDungeon dungeon : filteredDungeons) {
            if (slot >= 45) break;
            
            Button dungeonButton = createDungeonButton(dungeon, player);
            gui.addButton(slot, dungeonButton);
            slot++;
        }
        
        // Update filter button displays
        updateFilterButtonDisplays(gui, player, filter);
        
        // Update GUI for the player
        gui.updateButtons(player);
    }
    
    /**
     * Create a button for a dungeon
     */
    private static Button createDungeonButton(AbstractDungeon dungeon, Player player) {
        MythicPlayer mythicPlayer = MythicDungeons.inst().getMythicPlayer(player);
        
        // Determine button material based on dungeon status
        Material material = Material.GRASS_BLOCK; // Default for available
        if (!dungeon.hasAvailableInstances()) {
            material = Material.RED_CONCRETE; // Full
        } else if (mythicPlayer.getInstance() != null) {
            material = Material.ORANGE_CONCRETE; // Player in dungeon
        }
        
        String dungeonName = "&f&l" + dungeon.getDisplayName();
        Button button = new Button("dungeon_" + dungeon.getWorldName(), material, dungeonName);
        
        // Add dungeon information to lore
        button.addLoreComponent(Util.modernizeColorsComponent("&7Type: &f" + dungeon.getClass().getSimpleName().replace("Dungeon", "")));
        
        if (dungeon.isUseDifficultyLevels()) {
            button.addLoreComponent(Util.modernizeColorsComponent("&7Difficulties: &f" + dungeon.getDifficultyLevels().size()));
        }
        
        button.addLoreComponent(Util.modernizeColorsComponent("&7Available Instances: &f" + 
            (dungeon.hasAvailableInstances() ? "Yes" : "No")));
        
        if (dungeon.getMinPartySize() > 1) {
            button.addLoreComponent(Util.modernizeColorsComponent("&7Party Size: &f" + 
                dungeon.getMinPartySize() + "-" + dungeon.getMaxPartySize()));
        }
        
        button.addLoreComponent(Util.modernizeColorsComponent(""));
        
        if (dungeon.hasAvailableInstances() && mythicPlayer.getInstance() == null) {
            button.addLoreComponent(Util.modernizeColorsComponent("&a\u25b6 Left-click to join"));
            if (dungeon.isUseDifficultyLevels() && dungeon.isShowDifficultyMenu()) {
                button.addLoreComponent(Util.modernizeColorsComponent("&b\u25b6 Right-click for difficulty menu"));
            }
        } else if (!dungeon.hasAvailableInstances()) {
            button.addLoreComponent(Util.modernizeColorsComponent("&c\u25cf No instances available"));
        } else {
            button.addLoreComponent(Util.modernizeColorsComponent("&6\u25cf You are already in a dungeon"));
        }
        
        // Add click actions
        button.addAction("click", event -> {
            if (event.isLeftClick()) {
                handleDungeonJoin(player, dungeon);
            } else if (event.isRightClick() && dungeon.isUseDifficultyLevels() && dungeon.isShowDifficultyMenu()) {
                openDifficultyMenu(player, dungeon);
            }
        });
        
        return button;
    }
    
    /**
     * Handle dungeon join action
     */
    private static void handleDungeonJoin(Player player, AbstractDungeon dungeon) {
        MythicPlayer mythicPlayer = MythicDungeons.inst().getMythicPlayer(player);
        
        if (mythicPlayer.getInstance() != null) {
            player.sendMessage(Util.modernizeColorsComponent("&cYou are already in a dungeon!"));
            return;
        }
        
        if (!dungeon.hasAvailableInstances()) {
            player.sendMessage(Util.modernizeColorsComponent("&cNo instances available for this dungeon!"));
            return;
        }
        
        player.closeInventory();
        
        if (dungeon.isUseDifficultyLevels() && !dungeon.isShowDifficultyMenu()) {
            // Use default difficulty
            MythicDungeons.inst().sendToDungeon(player, dungeon.getWorldName(), "DEFAULT");
        } else if (dungeon.isUseDifficultyLevels()) {
            // Open difficulty selection
            openDifficultyMenu(player, dungeon);
        } else {
            // No difficulty system
            MythicDungeons.inst().sendToDungeon(player, dungeon.getWorldName());
        }
    }
    
    /**
     * Open difficulty selection menu for a dungeon
     */
    private static void openDifficultyMenu(Player player, AbstractDungeon dungeon) {
        // This would open the existing difficulty GUI or create a new enhanced one
        MythicDungeons.inst().getAvnAPI().openGUI(player, "difficulty_" + dungeon.getWorldName());
    }
    
    /**
     * Check if a dungeon matches the current filter
     */
    private static boolean matchesFilter(AbstractDungeon dungeon, DungeonFilter filter) {
        // Type filter
        if (filter.typeFilter != null && !filter.typeFilter.isEmpty()) {
            String dungeonType = dungeon.getClass().getSimpleName().replace("Dungeon", "").toLowerCase();
            if (!dungeonType.contains(filter.typeFilter.toLowerCase())) {
                return false;
            }
        }
        
        // Status filter
        if (filter.statusFilter != null) {
            switch (filter.statusFilter) {
                case AVAILABLE_ONLY:
                    if (!dungeon.hasAvailableInstances()) return false;
                    break;
                case FULL_ONLY:
                    if (dungeon.hasAvailableInstances()) return false;
                    break;
            }
        }
        
        return true;
    }
    
    /**
     * Cycle through difficulty filters
     */
    private static void cycleDifficultyFilter(Player player) {
        DungeonFilter filter = playerFilters.computeIfAbsent(player.getUniqueId(), k -> new DungeonFilter());
        // Implementation for cycling difficulty filters
        player.sendMessage(Util.modernizeColorsComponent("&7Difficulty filter updated"));
    }
    
    /**
     * Cycle through type filters
     */
    private static void cycleTypeFilter(Player player) {
        DungeonFilter filter = playerFilters.computeIfAbsent(player.getUniqueId(), k -> new DungeonFilter());
        String[] types = {"", "classic", "procedural", "continuous"};
        int currentIndex = Arrays.asList(types).indexOf(filter.typeFilter != null ? filter.typeFilter : "");
        filter.typeFilter = types[(currentIndex + 1) % types.length];
        if (filter.typeFilter.isEmpty()) filter.typeFilter = null;
        
        player.sendMessage(Util.modernizeColorsComponent("&7Type filter: &f" + 
            (filter.typeFilter != null ? filter.typeFilter : "All Types")));
    }
    
    /**
     * Cycle through status filters
     */
    private static void cycleStatusFilter(Player player) {
        DungeonFilter filter = playerFilters.computeIfAbsent(player.getUniqueId(), k -> new DungeonFilter());
        StatusFilter[] statuses = {null, StatusFilter.AVAILABLE_ONLY, StatusFilter.FULL_ONLY};
        int currentIndex = Arrays.asList(statuses).indexOf(filter.statusFilter);
        filter.statusFilter = statuses[(currentIndex + 1) % statuses.length];
        
        String statusName = filter.statusFilter != null ? filter.statusFilter.name() : "All Dungeons";
        player.sendMessage(Util.modernizeColorsComponent("&7Status filter: &f" + statusName));
    }
    
    /**
     * Clear all filters for a player
     */
    private static void clearFilters(Player player) {
        playerFilters.remove(player.getUniqueId());
        player.sendMessage(Util.modernizeColorsComponent("&7All filters cleared"));
    }
    
    /**
     * Update filter button displays
     */
    private static void updateFilterButtonDisplays(Window gui, Player player, DungeonFilter filter) {
        // Update difficulty filter button
        Button difficultyButton = gui.getButton(45);
        if (difficultyButton != null) {
            difficultyButton.clearLore();
            difficultyButton.addLoreComponent(Util.modernizeColorsComponent("&7Filter dungeons by difficulty"));
            difficultyButton.addLoreComponent(Util.modernizeColorsComponent("&7Current: &fAll Difficulties"));
            difficultyButton.addLoreComponent(Util.modernizeColorsComponent(""));
            difficultyButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to change filter"));
        }
        
        // Update type filter button
        Button typeButton = gui.getButton(46);
        if (typeButton != null) {
            typeButton.clearLore();
            typeButton.addLoreComponent(Util.modernizeColorsComponent("&7Filter dungeons by type"));
            typeButton.addLoreComponent(Util.modernizeColorsComponent("&7Current: &f" + 
                (filter.typeFilter != null ? filter.typeFilter : "All Types")));
            typeButton.addLoreComponent(Util.modernizeColorsComponent(""));
            typeButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to change filter"));
        }
        
        // Update status filter button
        Button statusButton = gui.getButton(47);
        if (statusButton != null) {
            statusButton.clearLore();
            statusButton.addLoreComponent(Util.modernizeColorsComponent("&7Filter by availability"));
            statusButton.addLoreComponent(Util.modernizeColorsComponent("&7Current: &f" + 
                (filter.statusFilter != null ? filter.statusFilter.name() : "All Dungeons")));
            statusButton.addLoreComponent(Util.modernizeColorsComponent(""));
            statusButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to change filter"));
        }
    }
    
    /**
     * Open the dungeon browser for a player
     */
    public static void openDungeonBrowser(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
    
    /**
     * Filter class for storing player filter preferences
     */
    private static class DungeonFilter {
        String difficultyFilter;
        String typeFilter;
        StatusFilter statusFilter;
    }
    
    /**
     * Enum for status filtering options
     */
    private enum StatusFilter {
        AVAILABLE_ONLY,
        FULL_ONLY
    }
}
