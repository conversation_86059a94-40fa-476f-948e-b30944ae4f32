/*    */ package javassist.bytecode;
/*    */ 
/*    */ import javassist.CannotCompileException;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DuplicateMemberException
/*    */   extends CannotCompileException
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   
/*    */   public DuplicateMemberException(String msg) {
/* 32 */     super(msg);
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Downloads\extract\MythicDungeons-2.0.1-SNAPSHOT.jar!\javassist\bytecode\DuplicateMemberException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */