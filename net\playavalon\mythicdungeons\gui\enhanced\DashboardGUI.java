package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.player.MythicPlayer;
import net.playavalon.mythicdungeons.api.parents.instances.AbstractInstance;
import net.playavalon.mythicdungeons.api.queue.QueueData;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import net.playavalon.mythicdungeons.utility.helpers.LangUtils;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;

/**
 * Enhanced Main Dashboard GUI - Central hub for all dungeon activities
 * Provides quick access to all major plugin features with visual status indicators
 */
public class DashboardGUI {
    
    private static final String GUI_NAME = "mythic_dashboard";
    private static final String GUI_TITLE = "&8\u2726 &6&lMythic Dungeons Dashboard &8\u2726";
    
    /**
     * Initialize the main dashboard GUI
     */
    public static void initDashboard() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        // Set up the dashboard layout
        setupDashboardButtons(gui);
        setupNavigationButtons(gui);
        setupStatusDisplay(gui);
        setupQuickActions(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_dashboard", event -> {
            Player player = (Player) event.getPlayer();
            loadPlayerDashboard(gui, player);
        });
    }
    
    /**
     * Set up the main dashboard navigation buttons
     */
    private static void setupDashboardButtons(Window gui) {
        // Dungeon Browser Button
        Button dungeonBrowser = createNavigationButton(
            Material.ENDER_CHEST,
            "&6&lDungeon Browser",
            Arrays.asList(
                "&7Explore available dungeons",
                "&7Filter by difficulty and type",
                "&7Preview rewards and requirements",
                "",
                "&e\u25b6 Click to browse dungeons"
            ),
            "dungeon_browser"
        );
        gui.addButton(10, dungeonBrowser);
        
        // Party Management Button
        Button partyManager = createNavigationButton(
            Material.PLAYER_HEAD,
            "&a&lParty Management",
            Arrays.asList(
                "&7Create or join a party",
                "&7Browse party listings",
                "&7Manage party settings",
                "",
                "&e\u25b6 Click to manage parties"
            ),
            "party_management"
        );
        gui.addButton(12, partyManager);
        
        // Player Statistics Button
        Button playerStats = createNavigationButton(
            Material.BOOK,
            "&b&lPlayer Statistics",
            Arrays.asList(
                "&7View your dungeon progress",
                "&7Check achievements and records",
                "&7Compare with leaderboards",
                "",
                "&e\u25b6 Click to view statistics"
            ),
            "player_statistics"
        );
        gui.addButton(14, playerStats);
        
        // Rewards Center Button
        Button rewardsCenter = createNavigationButton(
            Material.CHEST,
            "&d&lRewards Center",
            Arrays.asList(
                "&7Collect pending rewards",
                "&7View reward history",
                "&7Preview dungeon loot",
                "",
                "&e\u25b6 Click to access rewards"
            ),
            "rewards_center"
        );
        gui.addButton(16, rewardsCenter);
    }
    
    /**
     * Set up navigation and utility buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Settings Button
        Button settings = createUtilityButton(
            Material.REDSTONE,
            "&7Settings",
            Arrays.asList(
                "&7Configure your preferences",
                "&7Customize display options",
                "",
                "&e\u25b6 Click to open settings"
            ),
            "settings"
        );
        gui.addButton(45, settings);
        
        // Help Button
        Button help = createUtilityButton(
            Material.KNOWLEDGE_BOOK,
            "&7Help & Tutorials",
            Arrays.asList(
                "&7Learn how to use dungeons",
                "&7View command reference",
                "&7Get gameplay tips",
                "",
                "&e\u25b6 Click for help"
            ),
            "help_system"
        );
        gui.addButton(49, help);
        
        // Refresh Button
        Button refresh = createUtilityButton(
            Material.CLOCK,
            "&7Refresh Dashboard",
            Arrays.asList(
                "&7Update dashboard information",
                "",
                "&e\u25b6 Click to refresh"
            ),
            "refresh_dashboard"
        );
        gui.addButton(53, refresh);
    }
    
    /**
     * Set up status display area
     */
    private static void setupStatusDisplay(Window gui) {
        // Status display will be populated dynamically in loadPlayerDashboard
        // Reserve slots 19-25 for status information
    }
    
    /**
     * Set up quick action buttons
     */
    private static void setupQuickActions(Window gui) {
        // Quick Join Button
        Button quickJoin = createActionButton(
            Material.EMERALD,
            "&a&lQuick Join",
            Arrays.asList(
                "&7Join any available dungeon",
                "&7Automatically match difficulty",
                "",
                "&e\u25b6 Click for quick join"
            ),
            "quick_join"
        );
        gui.addButton(28, quickJoin);
        
        // Create Party Button
        Button createParty = createActionButton(
            Material.NETHER_STAR,
            "&b&lCreate Party",
            Arrays.asList(
                "&7Start a new party",
                "&7Invite friends to join",
                "",
                "&e\u25b6 Click to create party"
            ),
            "create_party"
        );
        gui.addButton(34, createParty);
    }
    
    /**
     * Create a navigation button with consistent styling
     */
    private static Button createNavigationButton(Material material, String name, List<String> lore, String action) {
        Button button = new Button(action, material, name);
        for (String line : lore) {
            button.addLoreComponent(Util.modernizeColorsComponent(line));
        }
        
        button.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            handleNavigation(player, action);
        });
        
        return button;
    }
    
    /**
     * Create a utility button with consistent styling
     */
    private static Button createUtilityButton(Material material, String name, List<String> lore, String action) {
        Button button = new Button(action, material, name);
        for (String line : lore) {
            button.addLoreComponent(Util.modernizeColorsComponent(line));
        }
        
        button.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            handleUtilityAction(player, action);
        });
        
        return button;
    }
    
    /**
     * Create an action button with consistent styling
     */
    private static Button createActionButton(Material material, String name, List<String> lore, String action) {
        Button button = new Button(action, material, name);
        for (String line : lore) {
            button.addLoreComponent(Util.modernizeColorsComponent(line));
        }
        
        button.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            handleQuickAction(player, action);
        });
        
        return button;
    }
    
    /**
     * Load player-specific dashboard content
     */
    private static void loadPlayerDashboard(Window gui, Player player) {
        MythicPlayer mythicPlayer = MythicDungeons.inst().getMythicPlayer(player);
        
        // Update status indicators
        updatePlayerStatus(gui, player, mythicPlayer);
        updatePartyStatus(gui, player, mythicPlayer);
        updateQueueStatus(gui, player, mythicPlayer);
        updateRecentActivity(gui, player, mythicPlayer);
    }
    
    /**
     * Update player status display
     */
    private static void updatePlayerStatus(Window gui, Player player, MythicPlayer mythicPlayer) {
        // Player status indicator (slot 19)
        Material statusMaterial = Material.GREEN_CONCRETE;
        String statusName = "&a&lOnline";
        List<String> statusLore = Arrays.asList(
            "&7Player: &f" + player.getName(),
            "&7Status: &aReady for dungeons",
            "",
            "&7Current World: &f" + player.getWorld().getName()
        );
        
        // Check if player is in dungeon
        AbstractInstance instance = mythicPlayer.getInstance();
        if (instance != null) {
            statusMaterial = Material.ORANGE_CONCRETE;
            statusName = "&6&lIn Dungeon";
            statusLore = Arrays.asList(
                "&7Player: &f" + player.getName(),
                "&7Status: &6In dungeon",
                "&7Dungeon: &f" + instance.getDungeon().getDisplayName(),
                "",
                "&e\u25b6 Click to return to dungeon"
            );
        }
        
        Button statusButton = new Button("player_status", statusMaterial, statusName);
        for (String line : statusLore) {
            statusButton.addLoreComponent(Util.modernizeColorsComponent(line));
        }
        
        if (instance != null) {
            statusButton.addAction("click", event -> {
                player.closeInventory();
                // Teleport player back to dungeon if they're not already there
                if (!player.getWorld().equals(instance.getInstanceWorld())) {
                    mythicPlayer.sendToCheckpoint();
                }
            });
        }
        
        gui.addButton(19, statusButton);
    }
    
    /**
     * Handle navigation button clicks
     */
    private static void handleNavigation(Player player, String action) {
        switch (action) {
            case "dungeon_browser":
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_dungeon_browser");
                break;
            case "party_management":
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_party_management");
                break;
            case "player_statistics":
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_player_stats");
                break;
            case "rewards_center":
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_rewards_center");
                break;
        }
    }
    
    /**
     * Handle utility action clicks
     */
    private static void handleUtilityAction(Player player, String action) {
        switch (action) {
            case "settings":
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_settings");
                break;
            case "help_system":
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_help");
                break;
            case "refresh_dashboard":
                MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
                break;
        }
    }
    
    /**
     * Handle quick action clicks
     */
    private static void handleQuickAction(Player player, String action) {
        switch (action) {
            case "quick_join":
                // Implement quick join logic
                player.closeInventory();
                player.performCommand("md play");
                break;
            case "create_party":
                // Implement party creation
                if (MythicDungeons.inst().createParty(player)) {
                    player.sendMessage(Util.modernizeColorsComponent("&aParty created successfully!"));
                } else {
                    player.sendMessage(Util.modernizeColorsComponent("&cFailed to create party. You may already be in one."));
                }
                break;
        }
    }

    /**
     * Update party status display
     */
    private static void updatePartyStatus(Window gui, Player player, MythicPlayer mythicPlayer) {
        Material partyMaterial = Material.GRAY_CONCRETE;
        String partyName = "&7&lNo Party";
        List<String> partyLore = Arrays.asList(
            "&7You are not in a party",
            "",
            "&e\u25b6 Click to create or join a party"
        );

        if (mythicPlayer.getDungeonParty() != null) {
            partyMaterial = Material.BLUE_CONCRETE;
            partyName = "&b&lIn Party";
            partyLore = Arrays.asList(
                "&7Party Members: &f" + mythicPlayer.getDungeonParty().getPlayers().size(),
                "&7Leader: &f" + mythicPlayer.getDungeonParty().getLeader().getName(),
                "",
                "&e\u25b6 Click to manage party"
            );
        }

        Button partyButton = new Button("party_status", partyMaterial, partyName);
        for (String line : partyLore) {
            partyButton.addLoreComponent(Util.modernizeColorsComponent(line));
        }

        partyButton.addAction("click", event -> {
            MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_party_management");
        });

        gui.addButton(21, partyButton);
    }

    /**
     * Update queue status display
     */
    private static void updateQueueStatus(Window gui, Player player, MythicPlayer mythicPlayer) {
        QueueData queue = MythicDungeons.inst().getQueueManager().getQueue(mythicPlayer);

        Material queueMaterial = Material.GRAY_CONCRETE;
        String queueName = "&7&lNot Queued";
        List<String> queueLore = Arrays.asList(
            "&7You are not in any queue",
            "",
            "&e\u25b6 Click to browse dungeons"
        );

        if (queue != null) {
            queueMaterial = Material.YELLOW_CONCRETE;
            queueName = "&e&lIn Queue";
            queueLore = Arrays.asList(
                "&7Dungeon: &f" + queue.getDungeon().getDisplayName(),
                "&7Difficulty: &f" + queue.getDifficulty(),
                "&7Position: &f" + (MythicDungeons.inst().getQueueManager().getPosition(queue) + 1),
                "",
                "&e\u25b6 Click to leave queue"
            );
        }

        Button queueButton = new Button("queue_status", queueMaterial, queueName);
        for (String line : queueLore) {
            queueButton.addLoreComponent(Util.modernizeColorsComponent(line));
        }

        queueButton.addAction("click", event -> {
            if (queue != null) {
                MythicDungeons.inst().getQueueManager().dequeue(mythicPlayer);
                player.sendMessage(Util.modernizeColorsComponent("&cLeft queue for " + queue.getDungeon().getDisplayName()));
                MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME); // Refresh dashboard
            } else {
                MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_dungeon_browser");
            }
        });

        gui.addButton(23, queueButton);
    }

    /**
     * Update recent activity display
     */
    private static void updateRecentActivity(Window gui, Player player, MythicPlayer mythicPlayer) {
        // Recent activity indicator (slot 25)
        Button activityButton = new Button("recent_activity", Material.CLOCK, "&6&lRecent Activity");
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&7Last dungeon: &fNone"));
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&7Dungeons completed: &f0"));
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&7Total playtime: &f0h 0m"));
        activityButton.addLoreComponent(Util.modernizeColorsComponent(""));
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click for detailed statistics"));

        activityButton.addAction("click", event -> {
            MythicDungeons.inst().getAvnAPI().openGUI(player, "enhanced_player_stats");
        });

        gui.addButton(25, activityButton);
    }

    /**
     * Open the main dashboard for a player
     */
    public static void openDashboard(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
}
