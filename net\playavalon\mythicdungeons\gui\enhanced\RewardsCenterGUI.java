package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.player.MythicPlayer;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Arrays;

/**
 * Enhanced Rewards Center GUI - Comprehensive reward management system
 * Features reward collection, history tracking, preview system, and organization
 */
public class RewardsCenterGUI {
    
    private static final String GUI_NAME = "enhanced_rewards_center";
    private static final String GUI_TITLE = "&8\u2726 &d&lRewards Center &8\u2726";
    
    /**
     * Initialize the enhanced rewards center GUI
     */
    public static void initRewardsCenter() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupRewardCategories(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_rewards", event -> {
            Player player = (Player) event.getPlayer();
            loadPlayerRewards(gui, player);
        });
    }
    
    /**
     * Set up reward category buttons
     */
    private static void setupRewardCategories(Window gui) {
        // Pending Rewards
        Button pendingRewards = new Button("pending_rewards", Material.CHEST, "&e&lPending Rewards");
        pendingRewards.addLoreComponent(Util.modernizeColorsComponent("&7Rewards waiting to be collected"));
        pendingRewards.addLoreComponent(Util.modernizeColorsComponent("&7From completed dungeons"));
        pendingRewards.addLoreComponent(Util.modernizeColorsComponent("&7Click to collect all"));
        pendingRewards.addLoreComponent(Util.modernizeColorsComponent(""));
        pendingRewards.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view pending"));
        
        pendingRewards.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showPendingRewards(player);
        });
        gui.addButton(10, pendingRewards);
        
        // Collected Rewards
        Button collectedRewards = new Button("collected_rewards", Material.ENDER_CHEST, "&a&lCollected Rewards");
        collectedRewards.addLoreComponent(Util.modernizeColorsComponent("&7View your reward collection"));
        collectedRewards.addLoreComponent(Util.modernizeColorsComponent("&7Browse by category"));
        collectedRewards.addLoreComponent(Util.modernizeColorsComponent("&7Search and filter items"));
        collectedRewards.addLoreComponent(Util.modernizeColorsComponent(""));
        collectedRewards.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to browse collection"));
        
        collectedRewards.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showCollectedRewards(player);
        });
        gui.addButton(12, collectedRewards);
        
        // Reward History
        Button rewardHistory = new Button("reward_history", Material.BOOK, "&b&lReward History");
        rewardHistory.addLoreComponent(Util.modernizeColorsComponent("&7View past reward collections"));
        rewardHistory.addLoreComponent(Util.modernizeColorsComponent("&7Track reward sources"));
        rewardHistory.addLoreComponent(Util.modernizeColorsComponent("&7Analyze reward patterns"));
        rewardHistory.addLoreComponent(Util.modernizeColorsComponent(""));
        rewardHistory.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view history"));
        
        rewardHistory.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showRewardHistory(player);
        });
        gui.addButton(14, rewardHistory);
        
        // Loot Preview
        Button lootPreview = new Button("loot_preview", Material.SPYGLASS, "&6&lLoot Preview");
        lootPreview.addLoreComponent(Util.modernizeColorsComponent("&7Preview dungeon rewards"));
        lootPreview.addLoreComponent(Util.modernizeColorsComponent("&7Check drop rates"));
        lootPreview.addLoreComponent(Util.modernizeColorsComponent("&7Plan your runs"));
        lootPreview.addLoreComponent(Util.modernizeColorsComponent(""));
        lootPreview.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to preview loot"));
        
        lootPreview.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            showLootPreview(player);
        });
        gui.addButton(16, lootPreview);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(45, backButton);
        
        // Collect All Button
        Button collectAllButton = new Button("collect_all", Material.HOPPER, "&a&lCollect All");
        collectAllButton.addLoreComponent(Util.modernizeColorsComponent("&7Collect all pending rewards"));
        collectAllButton.addLoreComponent(Util.modernizeColorsComponent("&7Automatically organize items"));
        collectAllButton.addLoreComponent(Util.modernizeColorsComponent(""));
        collectAllButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to collect all"));
        
        collectAllButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            collectAllRewards(player);
        });
        gui.addButton(49, collectAllButton);
        
        // Refresh Button
        Button refreshButton = new Button("refresh_rewards", Material.CLOCK, "&7Refresh");
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&7Update reward information"));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent(""));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to refresh"));
        
        refreshButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            loadPlayerRewards(gui, player);
        });
        gui.addButton(53, refreshButton);
    }
    
    /**
     * Load and display player rewards
     */
    private static void loadPlayerRewards(Window gui, Player player) {
        MythicPlayer mythicPlayer = MythicDungeons.inst().getMythicPlayer(player);
        
        // Display rewards overview
        displayRewardsOverview(gui, player, mythicPlayer);
        
        gui.updateButtons(player);
    }
    
    /**
     * Display rewards overview in the center area
     */
    private static void displayRewardsOverview(Window gui, Player player, MythicPlayer mythicPlayer) {
        // Clear center area (slots 19-34)
        for (int i = 19; i <= 34; i++) {
            gui.removeButton(i);
        }
        
        // Rewards Summary (slot 22)
        Button rewardsSummary = new Button("rewards_summary", Material.GOLD_INGOT, "&6&lRewards Summary");
        rewardsSummary.addLoreComponent(Util.modernizeColorsComponent("&7Pending Rewards: &e0"));
        rewardsSummary.addLoreComponent(Util.modernizeColorsComponent("&7Total Collected: &a0"));
        rewardsSummary.addLoreComponent(Util.modernizeColorsComponent("&7Reward Value: &60 coins"));
        rewardsSummary.addLoreComponent(Util.modernizeColorsComponent(""));
        rewardsSummary.addLoreComponent(Util.modernizeColorsComponent("&7Select a category above"));
        rewardsSummary.addLoreComponent(Util.modernizeColorsComponent("&7to manage your rewards"));
        
        gui.addButton(22, rewardsSummary);
        
        // Quick reward stats
        displayQuickRewardStats(gui, player, mythicPlayer);
    }
    
    /**
     * Display quick reward statistics
     */
    private static void displayQuickRewardStats(Window gui, Player player, MythicPlayer mythicPlayer) {
        // Recent Rewards (slot 20)
        Button recentRewards = new Button("recent_rewards", Material.EMERALD, "&a&lRecent Rewards");
        recentRewards.addLoreComponent(Util.modernizeColorsComponent("&7Last 24 hours: &f0 items"));
        recentRewards.addLoreComponent(Util.modernizeColorsComponent("&7Last week: &f0 items"));
        recentRewards.addLoreComponent(Util.modernizeColorsComponent("&7Most recent: &fNone"));
        gui.addButton(20, recentRewards);
        
        // Valuable Items (slot 21)
        Button valuableItems = new Button("valuable_items", Material.DIAMOND, "&b&lValuable Items");
        valuableItems.addLoreComponent(Util.modernizeColorsComponent("&7Rare items: &f0"));
        valuableItems.addLoreComponent(Util.modernizeColorsComponent("&7Epic items: &f0"));
        valuableItems.addLoreComponent(Util.modernizeColorsComponent("&7Legendary items: &f0"));
        gui.addButton(21, valuableItems);
        
        // Reward Streaks (slot 23)
        Button rewardStreaks = new Button("reward_streaks", Material.FIRE_CHARGE, "&c&lReward Streaks");
        rewardStreaks.addLoreComponent(Util.modernizeColorsComponent("&7Current streak: &f0 days"));
        rewardStreaks.addLoreComponent(Util.modernizeColorsComponent("&7Best streak: &f0 days"));
        rewardStreaks.addLoreComponent(Util.modernizeColorsComponent("&7Streak bonus: &f0%"));
        gui.addButton(23, rewardStreaks);
        
        // Collection Progress (slot 24)
        Button collectionProgress = new Button("collection_progress", Material.CHEST_MINECART, "&d&lCollection Progress");
        collectionProgress.addLoreComponent(Util.modernizeColorsComponent("&7Sets completed: &f0/0"));
        collectionProgress.addLoreComponent(Util.modernizeColorsComponent("&7Completion rate: &f0%"));
        collectionProgress.addLoreComponent(Util.modernizeColorsComponent("&7Next milestone: &fN/A"));
        gui.addButton(24, collectionProgress);
    }
    
    /**
     * Show pending rewards
     */
    private static void showPendingRewards(Player player) {
        // Open the existing rewards GUI or create enhanced version
        MythicDungeons.inst().getAvnAPI().openGUI(player, "rewards");
    }
    
    /**
     * Show collected rewards
     */
    private static void showCollectedRewards(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening collected rewards..."));
        // Implementation for collected rewards browser
    }
    
    /**
     * Show reward history
     */
    private static void showRewardHistory(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening reward history..."));
        // Implementation for reward history view
    }
    
    /**
     * Show loot preview
     */
    private static void showLootPreview(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening loot preview..."));
        // Implementation for loot preview system
    }
    
    /**
     * Collect all pending rewards
     */
    private static void collectAllRewards(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&aCollecting all pending rewards..."));
        // Implementation for collecting all rewards
        player.performCommand("rewards collect");
    }
    
    /**
     * Open the rewards center GUI for a player
     */
    public static void openRewardsCenter(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
}
