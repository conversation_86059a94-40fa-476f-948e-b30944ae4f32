package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Enhanced Settings GUI - Player preference and configuration management
 * Features display settings, notifications, keybinds, and accessibility options
 */
public class SettingsGUI {
    
    private static final String GUI_NAME = "enhanced_settings";
    private static final String GUI_TITLE = "&8\u2726 &7&lSettings &8\u2726";
    
    // Player settings storage (in production, this would be saved to file/database)
    private static final Map<UUID, PlayerSettings> playerSettings = new HashMap<>();
    
    /**
     * Initialize the enhanced settings GUI
     */
    public static void initSettings() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupSettingsCategories(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_settings", event -> {
            Player player = (Player) event.getPlayer();
            loadPlayerSettings(gui, player);
        });
    }
    
    /**
     * Set up settings category buttons
     */
    private static void setupSettingsCategories(Window gui) {
        // Display Settings
        Button displaySettings = new Button("display_settings", Material.PAINTING, "&6&lDisplay Settings");
        displaySettings.addLoreComponent(Util.modernizeColorsComponent("&7Customize GUI appearance"));
        displaySettings.addLoreComponent(Util.modernizeColorsComponent("&7Color themes and layouts"));
        displaySettings.addLoreComponent(Util.modernizeColorsComponent("&7Animation preferences"));
        displaySettings.addLoreComponent(Util.modernizeColorsComponent(""));
        displaySettings.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to configure"));
        
        displaySettings.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openDisplaySettings(player);
        });
        gui.addButton(10, displaySettings);
        
        // Notification Settings
        Button notificationSettings = new Button("notification_settings", Material.BELL, "&e&lNotification Settings");
        notificationSettings.addLoreComponent(Util.modernizeColorsComponent("&7Configure alerts and messages"));
        notificationSettings.addLoreComponent(Util.modernizeColorsComponent("&7Party invitations"));
        notificationSettings.addLoreComponent(Util.modernizeColorsComponent("&7Dungeon announcements"));
        notificationSettings.addLoreComponent(Util.modernizeColorsComponent(""));
        notificationSettings.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to configure"));
        
        notificationSettings.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openNotificationSettings(player);
        });
        gui.addButton(12, notificationSettings);
        
        // Gameplay Settings
        Button gameplaySettings = new Button("gameplay_settings", Material.REDSTONE, "&c&lGameplay Settings");
        gameplaySettings.addLoreComponent(Util.modernizeColorsComponent("&7Auto-join preferences"));
        gameplaySettings.addLoreComponent(Util.modernizeColorsComponent("&7Quick action settings"));
        gameplaySettings.addLoreComponent(Util.modernizeColorsComponent("&7Difficulty preferences"));
        gameplaySettings.addLoreComponent(Util.modernizeColorsComponent(""));
        gameplaySettings.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to configure"));
        
        gameplaySettings.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openGameplaySettings(player);
        });
        gui.addButton(14, gameplaySettings);
        
        // Privacy Settings
        Button privacySettings = new Button("privacy_settings", Material.IRON_DOOR, "&9&lPrivacy Settings");
        privacySettings.addLoreComponent(Util.modernizeColorsComponent("&7Control data sharing"));
        privacySettings.addLoreComponent(Util.modernizeColorsComponent("&7Leaderboard visibility"));
        privacySettings.addLoreComponent(Util.modernizeColorsComponent("&7Party join permissions"));
        privacySettings.addLoreComponent(Util.modernizeColorsComponent(""));
        privacySettings.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to configure"));
        
        privacySettings.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openPrivacySettings(player);
        });
        gui.addButton(16, privacySettings);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(45, backButton);
        
        // Reset to Defaults Button
        Button resetButton = new Button("reset_settings", Material.BARRIER, "&c&lReset to Defaults");
        resetButton.addLoreComponent(Util.modernizeColorsComponent("&7Reset all settings to default"));
        resetButton.addLoreComponent(Util.modernizeColorsComponent("&cThis cannot be undone"));
        resetButton.addLoreComponent(Util.modernizeColorsComponent(""));
        resetButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to reset"));
        
        resetButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            resetPlayerSettings(player);
        });
        gui.addButton(49, resetButton);
        
        // Save Settings Button
        Button saveButton = new Button("save_settings", Material.EMERALD, "&a&lSave Settings");
        saveButton.addLoreComponent(Util.modernizeColorsComponent("&7Save current configuration"));
        saveButton.addLoreComponent(Util.modernizeColorsComponent("&7Apply changes"));
        saveButton.addLoreComponent(Util.modernizeColorsComponent(""));
        saveButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to save"));
        
        saveButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            savePlayerSettings(player);
        });
        gui.addButton(53, saveButton);
    }
    
    /**
     * Load and display player settings
     */
    private static void loadPlayerSettings(Window gui, Player player) {
        PlayerSettings settings = getPlayerSettings(player);
        
        // Display current settings overview
        displaySettingsOverview(gui, player, settings);
        
        gui.updateButtons(player);
    }
    
    /**
     * Display settings overview in the center area
     */
    private static void displaySettingsOverview(Window gui, Player player, PlayerSettings settings) {
        // Clear center area (slots 19-34)
        for (int i = 19; i <= 34; i++) {
            gui.removeButton(i);
        }
        
        // Settings Overview (slot 22)
        Button settingsOverview = new Button("settings_overview", Material.WRITABLE_BOOK, "&7&lCurrent Settings");
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent("&7Theme: &f" + settings.theme));
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent("&7Notifications: &f" + (settings.notificationsEnabled ? "Enabled" : "Disabled")));
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent("&7Auto-join: &f" + (settings.autoJoinEnabled ? "Enabled" : "Disabled")));
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent("&7Privacy: &f" + settings.privacyLevel));
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent(""));
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent("&7Select a category above"));
        settingsOverview.addLoreComponent(Util.modernizeColorsComponent("&7to modify your settings"));
        
        gui.addButton(22, settingsOverview);
        
        // Quick settings toggles
        displayQuickToggles(gui, player, settings);
    }
    
    /**
     * Display quick toggle buttons
     */
    private static void displayQuickToggles(Window gui, Player player, PlayerSettings settings) {
        // Notifications Toggle (slot 20)
        Material notifMaterial = settings.notificationsEnabled ? Material.GREEN_CONCRETE : Material.RED_CONCRETE;
        String notifStatus = settings.notificationsEnabled ? "&aEnabled" : "&cDisabled";
        
        Button notificationsToggle = new Button("toggle_notifications", notifMaterial, "&e&lNotifications " + notifStatus);
        notificationsToggle.addLoreComponent(Util.modernizeColorsComponent("&7Quick toggle notifications"));
        notificationsToggle.addLoreComponent(Util.modernizeColorsComponent(""));
        notificationsToggle.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to toggle"));
        
        notificationsToggle.addAction("click", event -> {
            toggleNotifications(player);
            loadPlayerSettings(gui, player);
        });
        gui.addButton(20, notificationsToggle);
        
        // Auto-join Toggle (slot 21)
        Material autoJoinMaterial = settings.autoJoinEnabled ? Material.GREEN_CONCRETE : Material.RED_CONCRETE;
        String autoJoinStatus = settings.autoJoinEnabled ? "&aEnabled" : "&cDisabled";
        
        Button autoJoinToggle = new Button("toggle_autojoin", autoJoinMaterial, "&b&lAuto-join " + autoJoinStatus);
        autoJoinToggle.addLoreComponent(Util.modernizeColorsComponent("&7Quick toggle auto-join"));
        autoJoinToggle.addLoreComponent(Util.modernizeColorsComponent(""));
        autoJoinToggle.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to toggle"));
        
        autoJoinToggle.addAction("click", event -> {
            toggleAutoJoin(player);
            loadPlayerSettings(gui, player);
        });
        gui.addButton(21, autoJoinToggle);
        
        // Theme Selector (slot 23)
        Button themeSelector = new Button("theme_selector", Material.CYAN_DYE, "&d&lTheme: &f" + settings.theme);
        themeSelector.addLoreComponent(Util.modernizeColorsComponent("&7Current theme: &f" + settings.theme));
        themeSelector.addLoreComponent(Util.modernizeColorsComponent("&7Available: Default, Dark, Colorful"));
        themeSelector.addLoreComponent(Util.modernizeColorsComponent(""));
        themeSelector.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to cycle themes"));
        
        themeSelector.addAction("click", event -> {
            cycleTheme(player);
            loadPlayerSettings(gui, player);
        });
        gui.addButton(23, themeSelector);
        
        // Privacy Level (slot 24)
        Button privacySelector = new Button("privacy_selector", Material.IRON_DOOR, "&9&lPrivacy: &f" + settings.privacyLevel);
        privacySelector.addLoreComponent(Util.modernizeColorsComponent("&7Current level: &f" + settings.privacyLevel));
        privacySelector.addLoreComponent(Util.modernizeColorsComponent("&7Available: Public, Friends, Private"));
        privacySelector.addLoreComponent(Util.modernizeColorsComponent(""));
        privacySelector.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to cycle privacy"));
        
        privacySelector.addAction("click", event -> {
            cyclePrivacyLevel(player);
            loadPlayerSettings(gui, player);
        });
        gui.addButton(24, privacySelector);
    }
    
    /**
     * Open display settings
     */
    private static void openDisplaySettings(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening display settings..."));
        // Implementation for detailed display settings
    }
    
    /**
     * Open notification settings
     */
    private static void openNotificationSettings(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening notification settings..."));
        // Implementation for detailed notification settings
    }
    
    /**
     * Open gameplay settings
     */
    private static void openGameplaySettings(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening gameplay settings..."));
        // Implementation for detailed gameplay settings
    }
    
    /**
     * Open privacy settings
     */
    private static void openPrivacySettings(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&7Opening privacy settings..."));
        // Implementation for detailed privacy settings
    }
    
    /**
     * Get player settings (create default if not exists)
     */
    private static PlayerSettings getPlayerSettings(Player player) {
        return playerSettings.computeIfAbsent(player.getUniqueId(), k -> new PlayerSettings());
    }
    
    /**
     * Toggle notifications for a player
     */
    private static void toggleNotifications(Player player) {
        PlayerSettings settings = getPlayerSettings(player);
        settings.notificationsEnabled = !settings.notificationsEnabled;
        player.sendMessage(Util.modernizeColorsComponent("&7Notifications " + 
            (settings.notificationsEnabled ? "&aenabled" : "&cdisabled")));
    }
    
    /**
     * Toggle auto-join for a player
     */
    private static void toggleAutoJoin(Player player) {
        PlayerSettings settings = getPlayerSettings(player);
        settings.autoJoinEnabled = !settings.autoJoinEnabled;
        player.sendMessage(Util.modernizeColorsComponent("&7Auto-join " + 
            (settings.autoJoinEnabled ? "&aenabled" : "&cdisabled")));
    }
    
    /**
     * Cycle through available themes
     */
    private static void cycleTheme(Player player) {
        PlayerSettings settings = getPlayerSettings(player);
        String[] themes = {"Default", "Dark", "Colorful"};
        int currentIndex = Arrays.asList(themes).indexOf(settings.theme);
        settings.theme = themes[(currentIndex + 1) % themes.length];
        player.sendMessage(Util.modernizeColorsComponent("&7Theme changed to: &f" + settings.theme));
    }
    
    /**
     * Cycle through privacy levels
     */
    private static void cyclePrivacyLevel(Player player) {
        PlayerSettings settings = getPlayerSettings(player);
        String[] levels = {"Public", "Friends", "Private"};
        int currentIndex = Arrays.asList(levels).indexOf(settings.privacyLevel);
        settings.privacyLevel = levels[(currentIndex + 1) % levels.length];
        player.sendMessage(Util.modernizeColorsComponent("&7Privacy level changed to: &f" + settings.privacyLevel));
    }
    
    /**
     * Reset player settings to defaults
     */
    private static void resetPlayerSettings(Player player) {
        playerSettings.put(player.getUniqueId(), new PlayerSettings());
        player.sendMessage(Util.modernizeColorsComponent("&aSettings reset to defaults!"));
        openSettings(player); // Refresh the GUI
    }
    
    /**
     * Save player settings
     */
    private static void savePlayerSettings(Player player) {
        // In production, this would save to file/database
        player.sendMessage(Util.modernizeColorsComponent("&aSettings saved successfully!"));
    }
    
    /**
     * Open the settings GUI for a player
     */
    public static void openSettings(Player player) {
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
    
    /**
     * Player settings data class
     */
    private static class PlayerSettings {
        String theme = "Default";
        boolean notificationsEnabled = true;
        boolean autoJoinEnabled = false;
        String privacyLevel = "Public";
        
        // Additional settings can be added here
        boolean soundEnabled = true;
        boolean animationsEnabled = true;
        int guiScale = 1;
    }
}
