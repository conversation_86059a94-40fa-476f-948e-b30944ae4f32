package javassist.compiler.ast;

import javassist.compiler.CompileError;

public class Visitor {
  public void atASTList(ASTList n) throws CompileError {}
  
  public void atPair(Pair n) throws CompileError {}
  
  public void atFieldDecl(FieldDecl n) throws CompileError {}
  
  public void atMethodDecl(MethodDecl n) throws CompileError {}
  
  public void atStmnt(Stmnt n) throws CompileError {}
  
  public void atDeclarator(Declarator n) throws CompileError {}
  
  public void atAssignExpr(AssignExpr n) throws CompileError {}
  
  public void atCondExpr(CondExpr n) throws CompileError {}
  
  public void atBinExpr(BinExpr n) throws CompileError {}
  
  public void atExpr(Expr n) throws CompileError {}
  
  public void atCallExpr(CallExpr n) throws CompileError {}
  
  public void atCastExpr(CastExpr n) throws CompileError {}
  
  public void atInstanceOfExpr(InstanceOfExpr n) throws CompileError {}
  
  public void atNewExpr(NewExpr n) throws CompileError {}
  
  public void atSymbol(Symbol n) throws CompileError {}
  
  public void atMember(Member n) throws CompileError {}
  
  public void atVariable(Variable n) throws CompileError {}
  
  public void atKeyword(Keyword n) throws CompileError {}
  
  public void atStringL(StringL n) throws CompileError {}
  
  public void atIntConst(IntConst n) throws CompileError {}
  
  public void atDoubleConst(DoubleConst n) throws CompileError {}
  
  public void atArrayInit(ArrayInit n) throws CompileError {}
}


/* Location:              C:\Users\<USER>\Downloads\extract\MythicDungeons-2.0.1-SNAPSHOT.jar!\javassist\compiler\ast\Visitor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */