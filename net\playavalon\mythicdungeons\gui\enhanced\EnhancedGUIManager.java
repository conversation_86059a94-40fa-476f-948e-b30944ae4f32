package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.entity.Player;

/**
 * Enhanced GUI Manager - Central management for all enhanced GUI components
 * Handles initialization, registration, and coordination of enhanced GUIs
 */
public class EnhancedGUIManager {
    
    private static boolean initialized = false;
    
    /**
     * Initialize all enhanced GUIs
     */
    public static void initializeEnhancedGUIs() {
        if (initialized) {
            return;
        }
        
        try {
            // Initialize core GUIs
            DashboardGUI.initDashboard();
            DungeonBrowserGUI.initDungeonBrowser();
            PartyManagementGUI.initPartyManagement();
            
            // Initialize additional GUIs
            PlayerStatsGUI.initPlayerStats();
            RewardsCenterGUI.initRewardsCenter();
            SettingsGUI.initSettings();
            HelpSystemGUI.initHelpSystem();
            
            initialized = true;
            
            MythicDungeons.inst().getComponentLogger().info(
                Util.modernizeColorsComponent("&d[Enhanced GUIs] All enhanced GUI systems initialized successfully!")
            );
            
        } catch (Exception e) {
            MythicDungeons.inst().getComponentLogger().error(
                "Failed to initialize enhanced GUIs: " + e.getMessage()
            );
            e.printStackTrace();
        }
    }
    
    /**
     * Open the main dashboard for a player
     */
    public static void openMainDashboard(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        DashboardGUI.openDashboard(player);
    }
    
    /**
     * Open the dungeon browser for a player
     */
    public static void openDungeonBrowser(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        DungeonBrowserGUI.openDungeonBrowser(player);
    }
    
    /**
     * Open the party management interface for a player
     */
    public static void openPartyManagement(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        PartyManagementGUI.openPartyManagement(player);
    }
    
    /**
     * Open the player statistics interface for a player
     */
    public static void openPlayerStats(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        PlayerStatsGUI.openPlayerStats(player);
    }
    
    /**
     * Open the rewards center for a player
     */
    public static void openRewardsCenter(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        RewardsCenterGUI.openRewardsCenter(player);
    }
    
    /**
     * Open the settings interface for a player
     */
    public static void openSettings(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        SettingsGUI.openSettings(player);
    }
    
    /**
     * Open the help system for a player
     */
    public static void openHelpSystem(Player player) {
        if (!initialized) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUIs are not initialized yet!"));
            return;
        }
        
        HelpSystemGUI.openHelpSystem(player);
    }
    
    /**
     * Check if enhanced GUIs are initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Reload all enhanced GUIs
     */
    public static void reloadEnhancedGUIs() {
        initialized = false;
        initializeEnhancedGUIs();
    }
}
