package net.playavalon.mythicdungeons.gui.enhanced;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.avngui.GUI.Buttons.Button;
import net.playavalon.mythicdungeons.avngui.GUI.Window;
import net.playavalon.mythicdungeons.api.parents.dungeons.AbstractDungeon;
import net.playavalon.mythicdungeons.api.parents.instances.AbstractInstance;
import net.playavalon.mythicdungeons.player.MythicPlayer;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.Arrays;
import java.util.Collection;

/**
 * Enhanced Admin Management GUI - Comprehensive administrative interface
 * Features dungeon management, player oversight, system configuration, and analytics
 */
public class AdminManagementGUI {
    
    private static final String GUI_NAME = "enhanced_admin_panel";
    private static final String GUI_TITLE = "&8\u2726 &c&lAdmin Management Panel &8\u2726";
    
    /**
     * Initialize the enhanced admin management GUI
     */
    public static void initAdminManagement() {
        Window gui = new Window(GUI_NAME, 54, GUI_TITLE);
        
        setupAdminCategories(gui);
        setupNavigationButtons(gui);
        
        // Add dynamic content loading
        gui.addOpenAction("load_admin_data", event -> {
            Player player = (Player) event.getPlayer();
            if (!hasAdminPermission(player)) {
                player.closeInventory();
                player.sendMessage(Util.modernizeColorsComponent("&cYou don't have permission to access the admin panel!"));
                return;
            }
            loadAdminData(gui, player);
        });
    }
    
    /**
     * Set up admin category buttons
     */
    private static void setupAdminCategories(Window gui) {
        // Dungeon Management
        Button dungeonManagement = new Button("dungeon_management", Material.ENDER_CHEST, "&6&lDungeon Management");
        dungeonManagement.addLoreComponent(Util.modernizeColorsComponent("&7Manage all dungeons"));
        dungeonManagement.addLoreComponent(Util.modernizeColorsComponent("&7Create, edit, and delete"));
        dungeonManagement.addLoreComponent(Util.modernizeColorsComponent("&7Monitor active instances"));
        dungeonManagement.addLoreComponent(Util.modernizeColorsComponent(""));
        dungeonManagement.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to manage dungeons"));
        
        dungeonManagement.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openDungeonManagement(player);
        });
        gui.addButton(10, dungeonManagement);
        
        // Player Oversight
        Button playerOversight = new Button("player_oversight", Material.PLAYER_HEAD, "&a&lPlayer Oversight");
        playerOversight.addLoreComponent(Util.modernizeColorsComponent("&7Monitor player activities"));
        playerOversight.addLoreComponent(Util.modernizeColorsComponent("&7View player statistics"));
        playerOversight.addLoreComponent(Util.modernizeColorsComponent("&7Manage player data"));
        playerOversight.addLoreComponent(Util.modernizeColorsComponent(""));
        playerOversight.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to oversee players"));
        
        playerOversight.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openPlayerOversight(player);
        });
        gui.addButton(12, playerOversight);
        
        // System Configuration
        Button systemConfig = new Button("system_config", Material.REDSTONE, "&c&lSystem Configuration");
        systemConfig.addLoreComponent(Util.modernizeColorsComponent("&7Configure plugin settings"));
        systemConfig.addLoreComponent(Util.modernizeColorsComponent("&7Manage permissions"));
        systemConfig.addLoreComponent(Util.modernizeColorsComponent("&7System maintenance"));
        systemConfig.addLoreComponent(Util.modernizeColorsComponent(""));
        systemConfig.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to configure system"));
        
        systemConfig.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openSystemConfiguration(player);
        });
        gui.addButton(14, systemConfig);
        
        // Analytics Dashboard
        Button analytics = new Button("analytics", Material.MAP, "&b&lAnalytics Dashboard");
        analytics.addLoreComponent(Util.modernizeColorsComponent("&7View server statistics"));
        analytics.addLoreComponent(Util.modernizeColorsComponent("&7Performance metrics"));
        analytics.addLoreComponent(Util.modernizeColorsComponent("&7Usage reports"));
        analytics.addLoreComponent(Util.modernizeColorsComponent(""));
        analytics.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to view analytics"));
        
        analytics.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openAnalytics(player);
        });
        gui.addButton(16, analytics);
        
        // Quick Actions
        setupQuickActions(gui);
    }
    
    /**
     * Set up quick action buttons
     */
    private static void setupQuickActions(Window gui) {
        // Reload All Dungeons
        Button reloadDungeons = new Button("reload_dungeons", Material.CLOCK, "&e&lReload All Dungeons");
        reloadDungeons.addLoreComponent(Util.modernizeColorsComponent("&7Reload all dungeon configurations"));
        reloadDungeons.addLoreComponent(Util.modernizeColorsComponent("&cThis will kick all players"));
        reloadDungeons.addLoreComponent(Util.modernizeColorsComponent(""));
        reloadDungeons.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to reload"));
        
        reloadDungeons.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            reloadAllDungeons(player);
        });
        gui.addButton(28, reloadDungeons);
        
        // Clear All Queues
        Button clearQueues = new Button("clear_queues", Material.BARRIER, "&c&lClear All Queues");
        clearQueues.addLoreComponent(Util.modernizeColorsComponent("&7Clear all dungeon queues"));
        clearQueues.addLoreComponent(Util.modernizeColorsComponent("&7Remove waiting players"));
        clearQueues.addLoreComponent(Util.modernizeColorsComponent(""));
        clearQueues.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to clear"));
        
        clearQueues.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            clearAllQueues(player);
        });
        gui.addButton(30, clearQueues);
        
        // Emergency Stop
        Button emergencyStop = new Button("emergency_stop", Material.TNT, "&4&lEmergency Stop");
        emergencyStop.addLoreComponent(Util.modernizeColorsComponent("&7Stop all dungeon activities"));
        emergencyStop.addLoreComponent(Util.modernizeColorsComponent("&4WARNING: Use only in emergencies"));
        emergencyStop.addLoreComponent(Util.modernizeColorsComponent(""));
        emergencyStop.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to emergency stop"));
        
        emergencyStop.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            emergencyStop(player);
        });
        gui.addButton(32, emergencyStop);
        
        // Backup System
        Button backupSystem = new Button("backup_system", Material.CHEST, "&9&lBackup System");
        backupSystem.addLoreComponent(Util.modernizeColorsComponent("&7Create system backups"));
        backupSystem.addLoreComponent(Util.modernizeColorsComponent("&7Restore from backups"));
        backupSystem.addLoreComponent(Util.modernizeColorsComponent(""));
        backupSystem.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to manage backups"));
        
        backupSystem.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            openBackupSystem(player);
        });
        gui.addButton(34, backupSystem);
    }
    
    /**
     * Set up navigation buttons
     */
    private static void setupNavigationButtons(Window gui) {
        // Back to Dashboard Button
        Button backButton = new Button("back_dashboard", Material.ARROW, "&7\u2190 Back to Dashboard");
        backButton.addLoreComponent(Util.modernizeColorsComponent("&7Return to main dashboard"));
        backButton.addLoreComponent(Util.modernizeColorsComponent(""));
        backButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to go back"));
        
        backButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            DashboardGUI.openDashboard(player);
        });
        gui.addButton(45, backButton);
        
        // Refresh Data Button
        Button refreshButton = new Button("refresh_admin", Material.CLOCK, "&7Refresh Data");
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&7Update admin information"));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent(""));
        refreshButton.addLoreComponent(Util.modernizeColorsComponent("&e\u25b6 Click to refresh"));
        
        refreshButton.addAction("click", event -> {
            Player player = (Player) event.getWhoClicked();
            loadAdminData(gui, player);
        });
        gui.addButton(53, refreshButton);
    }
    
    /**
     * Load admin data and display overview
     */
    private static void loadAdminData(Window gui, Player player) {
        // Display system overview
        displaySystemOverview(gui, player);
        
        gui.updateButtons(player);
    }
    
    /**
     * Display system overview in the center area
     */
    private static void displaySystemOverview(Window gui, Player player) {
        // Clear center area (slots 19-25)
        for (int i = 19; i <= 25; i++) {
            gui.removeButton(i);
        }
        
        // System Status (slot 22)
        Collection<AbstractDungeon> dungeons = MythicDungeons.inst().getDungeons().getAll();
        int activePlayers = Bukkit.getOnlinePlayers().size();
        int activeInstances = MythicDungeons.inst().getActiveInstances().size();
        
        Button systemStatus = new Button("system_status", Material.EMERALD, "&a&lSystem Status");
        systemStatus.addLoreComponent(Util.modernizeColorsComponent("&7Total Dungeons: &f" + dungeons.size()));
        systemStatus.addLoreComponent(Util.modernizeColorsComponent("&7Active Instances: &f" + activeInstances));
        systemStatus.addLoreComponent(Util.modernizeColorsComponent("&7Online Players: &f" + activePlayers));
        systemStatus.addLoreComponent(Util.modernizeColorsComponent("&7System Status: &aHealthy"));
        systemStatus.addLoreComponent(Util.modernizeColorsComponent(""));
        systemStatus.addLoreComponent(Util.modernizeColorsComponent("&7Select a category above"));
        systemStatus.addLoreComponent(Util.modernizeColorsComponent("&7to manage the system"));
        
        gui.addButton(22, systemStatus);
        
        // Quick stats around the overview
        displayQuickAdminStats(gui, dungeons, activeInstances, activePlayers);
    }
    
    /**
     * Display quick admin statistics
     */
    private static void displayQuickAdminStats(Window gui, Collection<AbstractDungeon> dungeons, int activeInstances, int activePlayers) {
        // Active Instances (slot 20)
        Button instancesButton = new Button("quick_instances", Material.ENDER_PEARL, "&6&lActive Instances");
        instancesButton.addLoreComponent(Util.modernizeColorsComponent("&7Running: &f" + activeInstances));
        instancesButton.addLoreComponent(Util.modernizeColorsComponent("&7Max Allowed: &f" + MythicDungeons.inst().getConfig().getInt("General.MaxInstances", 10)));
        instancesButton.addLoreComponent(Util.modernizeColorsComponent("&7Load: &f" + Math.round((activeInstances / 10.0) * 100) + "%"));
        gui.addButton(20, instancesButton);
        
        // Player Activity (slot 21)
        int playersInDungeons = 0;
        for (Player p : Bukkit.getOnlinePlayers()) {
            MythicPlayer mp = MythicDungeons.inst().getMythicPlayer(p);
            if (mp.getInstance() != null) playersInDungeons++;
        }
        
        Button playersButton = new Button("quick_players", Material.PLAYER_HEAD, "&b&lPlayer Activity");
        playersButton.addLoreComponent(Util.modernizeColorsComponent("&7Online: &f" + activePlayers));
        playersButton.addLoreComponent(Util.modernizeColorsComponent("&7In Dungeons: &f" + playersInDungeons));
        playersButton.addLoreComponent(Util.modernizeColorsComponent("&7In Queues: &f" + MythicDungeons.inst().getQueueManager().getQueueCount()));
        gui.addButton(21, playersButton);
        
        // System Performance (slot 23)
        Button performanceButton = new Button("quick_performance", Material.REDSTONE, "&c&lSystem Performance");
        performanceButton.addLoreComponent(Util.modernizeColorsComponent("&7Memory Usage: &fNormal"));
        performanceButton.addLoreComponent(Util.modernizeColorsComponent("&7CPU Load: &fLow"));
        performanceButton.addLoreComponent(Util.modernizeColorsComponent("&7Response Time: &fGood"));
        gui.addButton(23, performanceButton);
        
        // Recent Activity (slot 24)
        Button activityButton = new Button("quick_activity", Material.CLOCK, "&d&lRecent Activity");
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&7Last Hour: &f0 completions"));
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&7Today: &f0 completions"));
        activityButton.addLoreComponent(Util.modernizeColorsComponent("&7Errors: &f0"));
        gui.addButton(24, activityButton);
    }
    
    /**
     * Check if player has admin permission
     */
    private static boolean hasAdminPermission(Player player) {
        return player.hasPermission("mythicdungeons.admin") || player.isOp();
    }
    
    /**
     * Open dungeon management interface
     */
    private static void openDungeonManagement(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&6Opening dungeon management..."));
        // Implementation for dungeon management interface
    }
    
    /**
     * Open player oversight interface
     */
    private static void openPlayerOversight(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&aOpening player oversight..."));
        // Implementation for player oversight interface
    }
    
    /**
     * Open system configuration interface
     */
    private static void openSystemConfiguration(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&cOpening system configuration..."));
        // Implementation for system configuration interface
    }
    
    /**
     * Open analytics dashboard
     */
    private static void openAnalytics(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&bOpening analytics dashboard..."));
        // Implementation for analytics dashboard
    }
    
    /**
     * Reload all dungeons
     */
    private static void reloadAllDungeons(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&eReloading all dungeons..."));
        MythicDungeons.inst().reloadAllDungeons();
        player.sendMessage(Util.modernizeColorsComponent("&aAll dungeons reloaded successfully!"));
    }
    
    /**
     * Clear all queues
     */
    private static void clearAllQueues(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&eClearing all queues..."));
        MythicDungeons.inst().getQueueManager().clearAllQueues();
        player.sendMessage(Util.modernizeColorsComponent("&aAll queues cleared!"));
    }
    
    /**
     * Emergency stop all dungeon activities
     */
    private static void emergencyStop(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&4EMERGENCY STOP ACTIVATED!"));
        // Implementation for emergency stop
        for (AbstractInstance instance : MythicDungeons.inst().getActiveInstances()) {
            instance.dispose();
        }
        player.sendMessage(Util.modernizeColorsComponent("&cAll dungeon activities stopped!"));
    }
    
    /**
     * Open backup system interface
     */
    private static void openBackupSystem(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&9Opening backup system..."));
        // Implementation for backup system interface
    }
    
    /**
     * Open the admin management GUI for a player
     */
    public static void openAdminManagement(Player player) {
        if (!hasAdminPermission(player)) {
            player.sendMessage(Util.modernizeColorsComponent("&cYou don't have permission to access the admin panel!"));
            return;
        }
        MythicDungeons.inst().getAvnAPI().openGUI(player, GUI_NAME);
    }
}
