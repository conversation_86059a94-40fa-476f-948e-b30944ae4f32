package net.playavalon.mythicdungeons.commands.dungeon;

import net.playavalon.mythicdungeons.MythicDungeons;
import net.playavalon.mythicdungeons.commands.Command;
import net.playavalon.mythicdungeons.gui.enhanced.EnhancedGUIManager;
import net.playavalon.mythicdungeons.utility.helpers.Util;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Enhanced Dashboard Command - Opens the new enhanced GUI dashboard
 * Provides access to the comprehensive dungeon management interface
 */
public class DashboardCommand extends Command {
    
    public DashboardCommand(MythicDungeons plugin, String command) {
        super(plugin, command);
    }
    
    @Override
    public boolean onCommand(CommandSender sender, org.bukkit.command.Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Util.modernizeColorsComponent("&cThis command can only be used by players!"));
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check if enhanced GUIs are initialized
        if (!EnhancedGUIManager.isInitialized()) {
            player.sendMessage(Util.modernizeColorsComponent("&cEnhanced GUI system is not available. Please contact an administrator."));
            return true;
        }
        
        // Handle subcommands
        if (args.length > 0) {
            String subcommand = args[0].toLowerCase();
            
            switch (subcommand) {
                case "dungeons":
                case "browse":
                    EnhancedGUIManager.openDungeonBrowser(player);
                    return true;
                    
                case "party":
                case "parties":
                    EnhancedGUIManager.openPartyManagement(player);
                    return true;
                    
                case "stats":
                case "statistics":
                    EnhancedGUIManager.openPlayerStats(player);
                    return true;
                    
                case "rewards":
                    EnhancedGUIManager.openRewardsCenter(player);
                    return true;
                    
                case "settings":
                case "config":
                    EnhancedGUIManager.openSettings(player);
                    return true;
                    
                case "help":
                    EnhancedGUIManager.openHelpSystem(player);
                    return true;
                    
                case "admin":
                    if (player.hasPermission("mythicdungeons.admin") || player.isOp()) {
                        net.playavalon.mythicdungeons.gui.enhanced.AdminManagementGUI.openAdminManagement(player);
                    } else {
                        player.sendMessage(Util.modernizeColorsComponent("&cYou don't have permission to access the admin panel!"));
                    }
                    return true;
                    
                case "reload":
                    if (player.hasPermission("mythicdungeons.admin") || player.isOp()) {
                        EnhancedGUIManager.reloadEnhancedGUIs();
                        player.sendMessage(Util.modernizeColorsComponent("&aEnhanced GUI system reloaded successfully!"));
                    } else {
                        player.sendMessage(Util.modernizeColorsComponent("&cYou don't have permission to reload the GUI system!"));
                    }
                    return true;
                    
                default:
                    showUsage(player);
                    return true;
            }
        }
        
        // Open main dashboard
        EnhancedGUIManager.openMainDashboard(player);
        return true;
    }
    
    /**
     * Show command usage information
     */
    private void showUsage(Player player) {
        player.sendMessage(Util.modernizeColorsComponent("&6&l=== Enhanced Dashboard Commands ==="));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard &7- Open main dashboard"));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard dungeons &7- Open dungeon browser"));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard party &7- Open party management"));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard stats &7- Open player statistics"));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard rewards &7- Open rewards center"));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard settings &7- Open settings"));
        player.sendMessage(Util.modernizeColorsComponent("&f/dashboard help &7- Open help system"));
        
        if (player.hasPermission("mythicdungeons.admin") || player.isOp()) {
            player.sendMessage(Util.modernizeColorsComponent("&c/dashboard admin &7- Open admin panel"));
            player.sendMessage(Util.modernizeColorsComponent("&c/dashboard reload &7- Reload GUI system"));
        }
    }
}
